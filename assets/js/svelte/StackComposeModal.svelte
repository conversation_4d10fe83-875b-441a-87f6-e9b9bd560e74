<script lang="ts">
	import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader } from '@sveltestrap/sveltestrap';
	import { getStackCompose } from '../api';
	import Highlight from 'svelte-highlight';
	import yaml from 'svelte-highlight/languages/yaml';
	import 'svelte-highlight/styles/intellij-light.css';

	interface Props {
		service: Service | null;
		isOpen?: boolean;
		toggle?: any;
	}

	let { service, isOpen = $bindable(false), toggle = () => (isOpen = !isOpen) }: Props = $props();

	let composeContent = $state('');
	let loading = $state(false);
	let error = $state(false);

	async function fetchStackCompose() {
		if (!service) return;

		loading = true;
		error = false;
		composeContent = '';

		try {
			composeContent = await getStackCompose(service.server, service.stack);
		} catch (e) {
			error = true;
			console.error('Erreur lors de la récupération du fichier compose:', e);
		}

		loading = false;
	}

	async function onOpening() {
		composeContent = '';
		error = false;
	}

	async function onOpen() {
		await fetchStackCompose();
	}
</script>

{#if service}
	<Modal {isOpen} {toggle} size="xl" on:open={onOpen} on:opening={onOpening}>
		<ModalHeader {toggle}>
			Fichier compose - {service.server} | {service.stack}
		</ModalHeader>
		<ModalBody>
			{#if loading}
				<div class="text-center">
					<div class="spinner-border text-primary" role="status">
						<span class="visually-hidden">Loading...</span>
					</div>
					<p class="mt-2">Chargement du fichier compose de la stack {service.stack}...</p>
				</div>
			{:else if error}
				<div class="alert alert-danger d-flex align-items-center" role="alert">
					<i class="fa-solid fa-circle-exclamation me-2"></i>
					Impossible de récupérer le fichier compose de la stack {service.stack}
				</div>
			{:else if composeContent}
				<div class="d-flex justify-content-between align-items-center mb-3">
					<h6 class="mb-0">Contenu du fichier compose.yml</h6>
				</div>
				<p class="fst-italic">
					Attention il s'agit de la version compilée par Docker, pas du vrai contenu du fichier
				</p>
				<div class="code-container">
					<Highlight language={yaml} code={composeContent} />
				</div>
			{/if}
		</ModalBody>
		<ModalFooter>
			<Button color="secondary" on:click={toggle}>Fermer</Button>
		</ModalFooter>
	</Modal>
{/if}

<style>
	.code-container {
		border: 1px solid #e9ecef;
		border-radius: 0.375rem;
		background-color: #f8f9fa;
		max-height: calc(100vh - 325px);
		overflow-y: auto;
	}

	:global(.code-container pre) {
		margin: 0;
		padding: 1rem;
		background: transparent !important;
		border: none;
		border-radius: 0;
	}

	:global(.code-container code) {
		font-size: 0.875rem;
		line-height: 1.5;
	}
</style>
