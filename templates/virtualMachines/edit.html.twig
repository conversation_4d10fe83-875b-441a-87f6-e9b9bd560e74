{% extends 'front.html.twig' %}

{% block title %}Modifier {{ virtualMachine.name ?? virtualMachine.ip }}{% endblock %}

{% form_theme form 'bootstrap_5_layout.html.twig' %}

{% block body %}
    <div class="container-sm pt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>Modifier la machine virtuelle</h2>
            <div class="btn-group">
                <a href="{{ path('app_virtual_machines_show', {id: virtualMachine.id}) }}" class="btn btn-outline-info">
                    <i class="fas fa-eye"></i> Voir
                </a>
                <a href="{{ path('app_virtual_machines_index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Retour à la liste
                </a>
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                {{ form_start(form) }}
                    <div class="row">
                        <div class="col-12">
                            {{ form_row(form.name) }}
                        </div>
                        <div class="col-12">
                            {{ form_row(form.ip) }}
                        </div>
                        <div class="col-12">
                            {{ form_row(form.phpVersion) }}
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2 mt-4">
                        <a href="{{ path('app_virtual_machines_index') }}" class="btn btn-outline-secondary">
                            Annuler
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Enregistrer les modifications
                        </button>
                    </div>
                {{ form_end(form) }}
            </div>
        </div>
    </div>
{% endblock %}
