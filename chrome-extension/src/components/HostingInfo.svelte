<script lang="ts">
	import type { Configuration, Service } from '@/popup/types.js';
	import { getContext } from 'svelte';

	let { configuration, service }: { configuration: Configuration; service: Service } = $props();

	let settings = getContext('settings');
</script>

<div class="hosting-info">
	<div class="hosting-title">🏠 Hébergement</div>
	<div class="hosting-details">
		{#if configuration.type}
			<div class="hosting-item">
				<span class="hosting-label">Type :</span>
				<span class="hosting-value">{configuration.type.toUpperCase()}</span>
			</div>
		{/if}

		{#if configuration.webId}
			<div class="hosting-item">
				<span class="hosting-label">Web ID :</span>
				<span class="hosting-value">{configuration.webId}</span>
				{#if configuration.type === 'swarm'}
					<a
						style="text-decoration: none;"
						href="https://tdb-swarm.int.alienor.net/front?search={configuration.webId}"
						target="_blank">🔎</a
					>
				{/if}
			</div>
		{/if}

		{#if configuration.server}
			<div class="hosting-item">
				<span class="hosting-label">Serveur :</span>
				<span class="hosting-value">{configuration.server}</span>
				{#if settings.dockerToolsEnabled && configuration.type === 'swarm'}
					<a
						style="text-decoration: none;"
						href="dockertools://open?action=ssh&server={configuration.server}&stack={configuration.webId}"
						target="_blank">🐳</a
					>
				{/if}
			</div>
		{/if}
	</div>
</div>

<style>
	.hosting-info {
		background: #f8f9fa;
		border: 1px solid #e9ecef;
		border-radius: 8px;
		padding: 12px;
		margin-bottom: 16px;
	}

	.hosting-title {
		font-size: 12px;
		font-weight: 600;
		color: #495057;
		margin-bottom: 8px;
		text-transform: uppercase;
		letter-spacing: 0.5px;
	}

	.hosting-details {
		display: grid;
		gap: 6px;
	}

	.hosting-item {
		display: flex;
		align-items: center;
		font-size: 11px;
	}

	.hosting-label {
		font-weight: 500;
		color: #6c757d;
		min-width: 60px;
		margin-right: 8px;
	}

	.hosting-value {
		color: #495057;
		font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
		background: #e9ecef;
		padding: 2px 6px;
		border-radius: 4px;
		font-size: 10px;
	}
</style>
