// La configuration est maintenant dans config.js

// Fonction pour obtenir l'URL de l'onglet actif
async function getCurrentTabUrl() {
	const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
	return tab.url;
}

// Fonction pour récupérer les données des webhosts (utilise le cache du service worker)
async function fetchWebHosts() {
	try {
		debugLog('Récupération des webhosts depuis le cache du service worker');

		// Demander les données au service worker (qui gère le cache)
		const response = await chrome.runtime.sendMessage({ action: 'getCachedWebHosts' });

		if (response && response.webHosts) {
			debugLog('Données reçues du service worker:', response.webHosts.length, 'webhosts');
			return response.webHosts;
		}

		// Fallback : récupération directe si le service worker ne répond pas
		debugLog("Fallback: récupération directe depuis l'API");
		return await fetchWebHostsDirect();
	} catch (error) {
		debugLog('Erreur lors de la récupération depuis le service worker, tentative directe:', error);
		return await fetchWebHostsDirect();
	}
}

// Fonction de fallback pour récupération directe
async function fetchWebHostsDirect() {
	try {
		debugLog('Récupération directe depuis:', `${CONFIG.API_BASE_URL}${CONFIG.API_ENDPOINT}`);

		const response = await fetch(`${CONFIG.API_BASE_URL}${CONFIG.API_ENDPOINT}`, {
			method: 'GET',
			headers: {
				Accept: 'application/json',
				'Content-Type': 'application/json'
			},
			timeout: CONFIG.API_TIMEOUT
		});

		if (!response.ok) {
			throw new Error(`HTTP ${response.status}: ${response.statusText}`);
		}

		const data = await response.json();
		debugLog('Données reçues directement:', data);

		return data.webHosts || [];
	} catch (error) {
		debugLog('Erreur lors de la récupération directe des webhosts:', error);

		// Gestion spécifique des erreurs CORS
		if (error.message.includes('CORS') || error.message.includes('fetch')) {
			throw new Error(
				`Impossible de se connecter à l'API (${CONFIG.API_BASE_URL}). Vérifiez que l'API est accessible et que les permissions CORS sont configurées.`
			);
		}

		throw error;
	}
}

// Fonction pour trouver le webhost correspondant à l'URL
function findMatchingWebHost(currentUrl, webHosts) {
	const currentDomain = extractDomain(currentUrl);
	debugLog('Recherche du webhost pour le domaine:', currentDomain);

	for (const webHost of webHosts) {
		debugLog('Vérification du webhost:', webHost.name);

		// Vérifier les URLs principales
		if (webHost.urls && webHost.urls.length > 0) {
			for (const urlObj of webHost.urls) {
				const webhostDomain = extractDomain(urlObj.url);
				debugLog('  - URL principale:', urlObj.url, '-> domaine:', webhostDomain);
				if (webhostDomain === currentDomain) {
					debugLog('✓ Correspondance trouvée avec URL principale');
					return webHost;
				}
			}
		}

		// Vérifier aussi les URLs associées
		if (webHost.associatedUrls && webHost.associatedUrls.length > 0) {
			for (const associatedUrl of webHost.associatedUrls) {
				const associatedDomain = extractDomain(associatedUrl.url);
				debugLog('  - URL associée:', associatedUrl.url, '-> domaine:', associatedDomain);
				if (associatedDomain === currentDomain) {
					debugLog('✓ Correspondance trouvée avec URL associée');
					return webHost;
				}
			}
		}
	}

	debugLog('✗ Aucune correspondance trouvée');
	return null;
}

// Fonction pour extraire le domaine d'une URL
function extractDomain(url) {
	try {
		const urlObj = new URL(url);
		return urlObj.hostname;
	} catch (error) {
		debugLog('URL invalide:', url, error);
		return '';
	}
}

// Fonction pour obtenir l'icône d'un lien
function getLinkIcon(type) {
	const icons = {
		gitlab: '🦊',
		confluence: '📖',
		database: '🗄️',
		url: '🌐',
		associated: '🔗',
		docker: '🐳'
	};

	return icons[type] || '🔗';
}

// Fonction pour convertir une URL Git SSH en URL HTTP
function convertGitUrlToHttp(gitUrl) {
	if (!gitUrl) {
		return gitUrl;
	}

	// Si c'est déjà une URL HTTP/HTTPS, la retourner telle quelle
	if (gitUrl.startsWith('http://') || gitUrl.startsWith('https://')) {
		return gitUrl;
	}

	// Si ce n'est pas une URL Git SSH, la retourner telle quelle
	if (!gitUrl.startsWith('git@')) {
		return gitUrl;
	}

	try {
		// Formats supportés:
		// git@hostname:username/repository.git
		// git@hostname:port/username/repository.git
		// git@hostname:username/repository

		debugLog('Conversion URL Git SSH:', gitUrl);

		// Extraire la partie après git@
		const withoutPrefix = gitUrl.substring(4); // Enlever "git@"

		// Séparer hostname et path
		const colonIndex = withoutPrefix.indexOf(':');
		if (colonIndex === -1) {
			debugLog('Format Git SSH invalide (pas de :):', gitUrl);
			return gitUrl;
		}

		const hostname = withoutPrefix.substring(0, colonIndex);
		let path = withoutPrefix.substring(colonIndex + 1);

		// Gérer le cas où il y a un port (ex: git@hostname:22/path)
		if (path.match(/^\d+\//)) {
			// Enlever le port du path
			const slashIndex = path.indexOf('/');
			if (slashIndex !== -1) {
				path = path.substring(slashIndex + 1);
			}
		}

		// Enlever l'extension .git si présente
		if (path.endsWith('.git')) {
			path = path.substring(0, path.length - 4);
		}

		// Construire l'URL HTTP
		const httpUrl = `https://${hostname}/${path}`;

		debugLog('URL Git convertie:', gitUrl, '->', httpUrl);
		return httpUrl;
	} catch (error) {
		debugLog("Erreur lors de la conversion de l'URL Git:", error);
		return gitUrl; // En cas d'erreur, retourner l'URL originale
	}
}

// Fonction pour créer un indicateur de statut
function createStatusIndicator(status) {
	const indicator = document.createElement('span');
	indicator.className = 'status-indicator';

	if (status === 'OK') {
		indicator.classList.add('status-ok');
		indicator.title = 'Service disponible';
	} else if (status === 'ERROR' || status === 'TIMEOUT') {
		indicator.classList.add('status-error');
		indicator.title = 'Service indisponible';
	} else {
		indicator.classList.add('status-unknown');
		indicator.title = 'Statut inconnu';
	}

	return indicator;
}

// Fonction pour créer le bloc d'informations d'hébergement
function createHostingInfoBlock(configuration) {
	const hostingInfo = document.createElement('div');
	hostingInfo.className = 'hosting-info';

	const hostingTitle = document.createElement('div');
	hostingTitle.className = 'hosting-title';
	hostingTitle.textContent = '🏠 Hébergement';

	const hostingDetails = document.createElement('div');
	hostingDetails.className = 'hosting-details';

	// Type
	if (configuration.type) {
		const typeItem = document.createElement('div');
		typeItem.className = 'hosting-item';

		const typeLabel = document.createElement('span');
		typeLabel.className = 'hosting-label';
		typeLabel.textContent = 'Type:';

		const typeValue = document.createElement('span');
		typeValue.className = 'hosting-value';
		typeValue.textContent = configuration.type.toUpperCase();

		typeItem.appendChild(typeLabel);
		typeItem.appendChild(typeValue);
		hostingDetails.appendChild(typeItem);
	}

	// Web ID
	if (configuration.webId) {
		const webIdItem = document.createElement('div');
		webIdItem.className = 'hosting-item';

		const webIdLabel = document.createElement('span');
		webIdLabel.className = 'hosting-label';
		webIdLabel.textContent = 'Web ID:';

		const webIdValue = document.createElement('span');
		webIdValue.className = 'hosting-value';
		webIdValue.textContent = configuration.webId;

		webIdItem.appendChild(webIdLabel);
		webIdItem.appendChild(webIdValue);
		hostingDetails.appendChild(webIdItem);
	}

	// Server
	if (configuration.server) {
		const serverItem = document.createElement('div');
		serverItem.className = 'hosting-item';

		const serverLabel = document.createElement('span');
		serverLabel.className = 'hosting-label';
		serverLabel.textContent = 'Serveur:';

		const serverValue = document.createElement('span');
		serverValue.className = 'hosting-value';
		serverValue.textContent = configuration.server;

		serverItem.appendChild(serverLabel);
		serverItem.appendChild(serverValue);
		hostingDetails.appendChild(serverItem);
	}

	hostingInfo.appendChild(hostingTitle);
	hostingInfo.appendChild(hostingDetails);

	return hostingInfo;
}

// Fonction pour créer le bloc d'informations techniques (PHP, Symfony, Docker)
function createTechInfoBlock(serviceDetails, serviceRepository) {
	if (!serviceDetails && !serviceRepository) {
		return null;
	}

	const techInfo = document.createElement('div');
	techInfo.className = 'tech-info';

	const techTitle = document.createElement('div');
	techTitle.className = 'tech-title';
	techTitle.textContent = '⚙️ Technologies';

	const techDetails = document.createElement('div');
	techDetails.className = 'tech-details';

	// PHP
	if (serviceDetails?.php) {
		const phpItem = document.createElement('div');
		phpItem.className = 'tech-item';

		const phpLabel = document.createElement('span');
		phpLabel.className = 'tech-label';
		phpLabel.textContent = 'PHP:';

		const phpValue = document.createElement('span');
		phpValue.className = 'tech-value';
		phpValue.textContent = serviceDetails.php.version;

		phpItem.appendChild(phpLabel);
		phpItem.appendChild(phpValue);

		// Statut PHP
		if (serviceDetails.php.is_eoled) {
			const phpStatus = document.createElement('span');
			phpStatus.className = 'tech-status eol';
			phpStatus.textContent = 'EOL';
			phpStatus.title = 'Version en fin de vie';
			phpItem.appendChild(phpStatus);
		} else if (!serviceDetails.php.is_latest) {
			const phpStatus = document.createElement('span');
			phpStatus.className = 'tech-status outdated';
			phpStatus.textContent = 'Ancienne';
			phpStatus.title = `Dernière version: ${serviceDetails.php.latest_patch_version}`;
			phpItem.appendChild(phpStatus);
		} else {
			const phpStatus = document.createElement('span');
			phpStatus.className = 'tech-status current';
			phpStatus.textContent = 'À jour';
			phpItem.appendChild(phpStatus);
		}

		techDetails.appendChild(phpItem);
	}

	// Symfony
	if (serviceDetails?.symfony) {
		const symfonyItem = document.createElement('div');
		symfonyItem.className = 'tech-item';

		const symfonyLabel = document.createElement('span');
		symfonyLabel.className = 'tech-label';
		symfonyLabel.textContent = 'Symfony:';

		const symfonyValue = document.createElement('span');
		symfonyValue.className = 'tech-value';
		symfonyValue.textContent = serviceDetails.symfony.version;

		symfonyItem.appendChild(symfonyLabel);
		symfonyItem.appendChild(symfonyValue);

		// Statut Symfony
		if (serviceDetails.symfony.is_eoled) {
			const symfonyStatus = document.createElement('span');
			symfonyStatus.className = 'tech-status eol';
			symfonyStatus.textContent = 'EOL';
			symfonyStatus.title = `Fin de vie: ${serviceDetails.symfony.eol}`;
			symfonyItem.appendChild(symfonyStatus);
		} else if (serviceDetails.symfony.is_eomed) {
			const symfonyStatus = document.createElement('span');
			symfonyStatus.className = 'tech-status outdated';
			symfonyStatus.textContent = 'EOM';
			symfonyStatus.title = `Fin de maintenance: ${serviceDetails.symfony.eom}`;
			symfonyItem.appendChild(symfonyStatus);
		} else if (!serviceDetails.symfony.is_latest) {
			const symfonyStatus = document.createElement('span');
			symfonyStatus.className = 'tech-status outdated';
			symfonyStatus.textContent = 'Ancienne';
			symfonyStatus.title = `Dernière version: ${serviceDetails.symfony.latest_patch_version}`;
			symfonyItem.appendChild(symfonyStatus);
		} else {
			const symfonyStatus = document.createElement('span');
			symfonyStatus.className = 'tech-status current';
			symfonyStatus.textContent = 'À jour';
			symfonyItem.appendChild(symfonyStatus);
		}

		techDetails.appendChild(symfonyItem);
	}

	// Image Docker
	if (serviceRepository?.image) {
		const dockerItem = document.createElement('div');
		dockerItem.className = 'tech-item';

		const dockerLabel = document.createElement('span');
		dockerLabel.className = 'tech-label';
		dockerLabel.textContent = 'Docker:';

		const dockerValue = document.createElement('span');
		dockerValue.className = 'tech-value';
		dockerValue.textContent = serviceRepository.tag || 'N/A';
		dockerValue.title = serviceRepository.image;

		dockerItem.appendChild(dockerLabel);
		dockerItem.appendChild(dockerValue);
		techDetails.appendChild(dockerItem);
	}

	// Si aucune information technique n'est disponible, ne pas afficher le bloc
	if (techDetails.children.length === 0) {
		return null;
	}

	techInfo.appendChild(techTitle);
	techInfo.appendChild(techDetails);

	return techInfo;
}

// Fonction pour afficher les informations du webhost
function displayWebHostInfo(webHost) {
	const content = document.getElementById('content');
	content.innerHTML = '';

	// Carte du webhost
	const webhostCard = document.createElement('div');
	webhostCard.className = 'webhost-card';

	const webhostHeader = document.createElement('div');
	webhostHeader.className = 'webhost-header';

	// Icône du webhost (première lettre du nom)
	const webhostIcon = document.createElement('div');
	webhostIcon.className = 'webhost-icon';
	webhostIcon.textContent = (webHost.name || 'W').charAt(0).toUpperCase();

	const webhostInfo = document.createElement('div');
	webhostInfo.className = 'webhost-info';

	const webhostName = document.createElement('div');
	webhostName.className = 'webhost-name';

	webhostName.textContent = webHost.name || 'Nom non défini';

	const webhostMeta = document.createElement('div');
	webhostMeta.className = 'webhost-meta';

	// Badge environnement
	if (webHost.environnement) {
		const envBadge = document.createElement('span');
		envBadge.className = 'meta-badge';
		envBadge.textContent = webHost.environnement;
		webhostMeta.appendChild(envBadge);
	}

	// Badge visibilité
	if (webHost.expectedVisibility) {
		const visBadge = document.createElement('span');
		visBadge.className = 'meta-badge';
		visBadge.textContent = webHost.expectedVisibility;
		webhostMeta.appendChild(visBadge);
	}

	webhostInfo.appendChild(webhostName);
	webhostInfo.appendChild(webhostMeta);

	webhostHeader.appendChild(webhostIcon);
	webhostHeader.appendChild(webhostInfo);
	webhostCard.appendChild(webhostHeader);

	// Informations Git si disponibles
	if (webHost.gitlabActiveBranch || webHost.lastCommitDate) {
		const gitInfo = document.createElement('div');
		gitInfo.style.fontSize = '11px';
		gitInfo.style.color = '#666';
		gitInfo.style.marginTop = '8px';
		gitInfo.style.paddingTop = '8px';
		gitInfo.style.borderTop = '1px solid #dee2e6';

		let gitText = '';
		if (webHost.gitlabActiveBranch) {
			gitText += `🌿 ${webHost.gitlabActiveBranch}`;
		}
		if (webHost.lastCommitDate) {
			if (gitText) gitText += ' • ';
			gitText += `📅 ${webHost.lastCommitDate}`;
		}

		gitInfo.textContent = gitText;
		webhostCard.appendChild(gitInfo);
	}

	content.appendChild(webhostCard);

	// Bloc hébergement
	if (webHost.configuration) {
		const hostingInfo = createHostingInfoBlock(webHost.configuration);
		content.appendChild(hostingInfo);
	}

	// Bloc informations techniques
	if (webHost.service) {
		const techInfo = createTechInfoBlock(webHost.service.details, webHost.service.repository);
		if (techInfo) {
			content.appendChild(techInfo);
		}
	}

	// Grille des liens
	const linksGrid = document.createElement('div');
	linksGrid.className = 'links-grid';

	// Collecter tous les liens
	const links = [];

	// GitLab
	if (webHost.gitlabRemoteUrl) {
		const gitlabUrl = convertGitUrlToHttp(webHost.gitlabRemoteUrl);
		links.push({
			type: 'gitlab',
			title: 'GitLab',
			url: gitlabUrl,
			originalUrl: webHost.gitlabRemoteUrl
		});
	}

	// Confluence
	if (webHost.confluenceUrl) {
		links.push({ type: 'confluence', title: 'Confluence', url: webHost.confluenceUrl });
	}

	// Base de données
	const databaseUrl = webHost.databaseUrl || webHost.databaseExplorerUrl;
	if (databaseUrl) {
		links.push({ type: 'database', title: 'Base de données', url: databaseUrl });
	}

	// URLs associées
	if (webHost.associatedUrls && webHost.associatedUrls.length > 0) {
		webHost.associatedUrls.forEach((associatedUrl) => {
			links.push({
				type: 'associated',
				title: 'Lien associé',
				url: associatedUrl.url
			});
		});
	}

	// Docker Tools Command (depuis le service)
	if (webHost.service && webHost.service.tasks && webHost.service.tasks.length > 0) {
		const firstTask = webHost.service.tasks[0];
		if (firstTask.dockerToolsCommand) {
			links.push({
				type: 'docker',
				title: 'Docker Tools',
				url: firstTask.dockerToolsCommand
			});
		}
	}

	// Docker Tools Command (depuis le service)
	if (webHost.service) {
		links.push({
			type: 'docker',
			title: 'TDB Swarm',
			label: 'Ouvrir dans TDB Swarm',
			url: 'https://tdb-swarm.int.alienor.net#' + webHost.service.id
		});

		if (webHost.service.links?.logs) {
			links.push({
				type: 'docker',
				title: 'Logs Docker',
				label: 'Ouvrir dans Swarmpit',
				url: webHost.service.links.logs
			});
		}
	}

	// Créer les cartes de liens
	links.forEach((link) => {
		const linkCard = createLinkCard(link);
		linksGrid.appendChild(linkCard);
	});

	content.appendChild(linksGrid);

	// Cette section est maintenant gérée dans la grille des liens ci-dessus

	// Ajouter l'info cache en mode debug
	if (CONFIG.DEBUG) {
		const cacheInfo = document.createElement('div');
		cacheInfo.className = 'cache-info';
		cacheInfo.innerHTML =
			'💾 Données mises en cache (1h) • <a href="#" class="clear-cache-link" id="clearCache">Vider le cache</a>';
		content.appendChild(cacheInfo);

		// Ajouter l'événement pour vider le cache
		const clearCacheLink = document.getElementById('clearCache');
		if (clearCacheLink) {
			clearCacheLink.addEventListener('click', async (e) => {
				e.preventDefault();
				try {
					await chrome.runtime.sendMessage({ action: 'clearCache' });
					debugLog('Cache vidé, rechargement...');
					window.location.reload();
				} catch (error) {
					debugLog('Erreur lors du vidage du cache:', error);
				}
			});
		}
	}
}

// Fonction pour créer une carte de lien
function createLinkCard(link) {
	const linkCard = document.createElement('div');
	linkCard.className = 'link-card';

	const linkMain = document.createElement('div');
	linkMain.className = 'link-main';

	const linkIcon = document.createElement('span');
	linkIcon.className = 'link-icon';
	linkIcon.textContent = getLinkIcon(link.type);

	const linkInfo = document.createElement('div');
	linkInfo.className = 'link-info';

	const linkTitle = document.createElement('div');
	linkTitle.className = 'link-title';
	linkTitle.textContent = link.title;

	const linkUrl = document.createElement('a');
	linkUrl.className = 'link-url';
	linkUrl.href = link.url;
	linkUrl.target = '_blank';

	// Afficher l'URL avec indication si elle a été convertie
	if (link.type === 'docker') {
		// Pour Docker Tools, afficher un texte personnalisé
		linkUrl.textContent = 'Ouvrir avec Docker Tools';
		linkUrl.title = `Clique pour ouvrir: ${link.url}`;
	} else if (link.originalUrl && link.originalUrl !== link.url) {
		// URL Git convertie
		linkUrl.textContent = link.originalUrl;
		linkUrl.title = `Clique pour ouvrir: ${link.url}`;
	} else {
		// URL normale
		linkUrl.textContent = link.url;
	}

	if (link.label) {
		linkUrl.textContent = link.label;
	}

	linkInfo.appendChild(linkTitle);
	linkInfo.appendChild(linkUrl);

	linkMain.appendChild(linkIcon);
	linkMain.appendChild(linkInfo);

	linkCard.appendChild(linkMain);

	// Ajouter l'indicateur de statut si disponible
	if (link.status) {
		const statusIndicator = createStatusIndicator(link.status);
		linkCard.appendChild(statusIndicator);
	}

	// Rendre toute la carte cliquable
	linkCard.addEventListener('click', (e) => {
		if (e.target !== linkUrl) {
			window.open(link.url, '_blank');
		}
	});

	return linkCard;
}

// Fonction pour afficher une erreur
function displayError(message) {
	const content = document.getElementById('content');
	content.innerHTML = `
        <div class="error">
            <strong>Erreur:</strong> ${message}
        </div>
    `;
}

// Fonction pour afficher "aucun résultat"
function displayNoMatch() {
	const content = document.getElementById('content');

	const noMatch = document.createElement('div');
	noMatch.className = 'no-match';
	noMatch.innerHTML = `
        <div>Aucun webhost trouvé pour cette URL</div>
        ${CONFIG.DEBUG ? '<div style="margin-top: 16px;"><a href="#" class="clear-cache-link" id="clearCache">🗑️ Vider le cache</a></div>' : ''}
    `;

	content.innerHTML = '';
	content.appendChild(noMatch);

	// Ajouter l'événement pour vider le cache (mode debug uniquement)
	if (CONFIG.DEBUG) {
		const clearCacheLink = document.getElementById('clearCache');
		if (clearCacheLink) {
			clearCacheLink.addEventListener('click', async (e) => {
				e.preventDefault();
				try {
					await chrome.runtime.sendMessage({ action: 'clearCache' });
					debugLog('Cache vidé, rechargement...');
					window.location.reload();
				} catch (error) {
					debugLog('Erreur lors du vidage du cache:', error);
				}
			});
		}
	}
}

// Fonction principale
async function main() {
	try {
		// Obtenir l'URL de l'onglet actif
		const currentUrl = await getCurrentTabUrl();
		debugLog("URL de l'onglet actif:", currentUrl);
		document.getElementById('currentUrl').textContent = currentUrl;

		// Récupérer les données des webhosts
		const webHosts = await fetchWebHosts();
		debugLog('Nombre de webhosts récupérés:', webHosts.length);

		// Trouver le webhost correspondant
		const matchingWebHost = findMatchingWebHost(currentUrl, webHosts);

		if (matchingWebHost) {
			debugLog('Webhost correspondant trouvé:', matchingWebHost.name);
			displayWebHostInfo(matchingWebHost);
		} else {
			debugLog('Aucun webhost correspondant trouvé');
			displayNoMatch();
		}
	} catch (error) {
		debugLog('Erreur dans main():', error);
		displayError(error.message);
	}
}

// Lancer l'application quand la popup est chargée
document.addEventListener('DOMContentLoaded', main);
