image: gitlab.alienor.net:5050/dev-interne/tdb-docker-swarm:dev

default:
  tags:
    - anetdev

cache:
  paths:
    - vendor/
    - assets/vendor/
  key:
    files:
      - composer.lock

stages:
  - build-extension
  - build-image
  - deploy

build-chrome-extension:
  stage: build-extension
  image: node:22-alpine
  needs: []
  before_script:
    - npm install --prefix chrome-extension
    - npm install -g crx3 web-ext
    - apk add curl
  script:
    - |
      BROWSER=chrome npm run build --prefix chrome-extension
      VERSION=$(grep '"version"' chrome-extension/dist/manifest.json | sed 's/.*"version": "\([^"]*\)".*/\1/')
      echo "Building Chrome Extension v${VERSION}..."
      mkdir -p dist
      crx3 --crx "./dist/alienor-webhost-extension-${VERSION}.crx" --key ./scripts/chrome-extension.pem ./chrome-extension/dist
      sh ./scripts/generate-update-xml.sh
      # Upload CRX file
      curl --header "JOB-TOKEN: $CI_JOB_TOKEN" \
           --upload-file "dist/alienor-webhost-extension-${VERSION}.crx" \
           "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/packages/generic/chrome-extension/${VERSION}/alienor-webhost-extension-${VERSION}.crx"
      curl --header "JOB-TOKEN: $CI_JOB_TOKEN" \
           --upload-file "dist/alienor-webhost-extension-${VERSION}.crx" \
           "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/packages/generic/chrome-extension/latest/alienor-webhost-extension.crx"

      # Upload update XML
      curl --header "JOB-TOKEN: $CI_JOB_TOKEN" \
           --upload-file "dist/updates.xml" \
           "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/packages/generic/chrome-extension/${VERSION}/updates.xml"

      # Upload update XML as latest
      curl --header "JOB-TOKEN: $CI_JOB_TOKEN" \
           --upload-file "dist/updates.xml" \
           "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/packages/generic/chrome-extension/latest/updates.xml"
    - |
      echo "Building Firefox Extension v${VERSION}..."
      BROWSER=firefox npm run build --prefix chrome-extension
      echo "${AMO_API_KEY}"
      echo "${AMO_API_SECRET}"
      web-ext sign --api-key="${AMO_API_KEY}" --api-secret="${AMO_API_SECRET}" --source-dir=./chrome-extension/dist --artifacts-dir=./dist --channel=unlisted
      mv ./dist/*.xpi "dist/alienor-webhost-extension-${VERSION}.xpi"

      # Upload XPI file
      curl --header "JOB-TOKEN: $CI_JOB_TOKEN" \
          --upload-file "dist/alienor-webhost-extension-${VERSION}.xpi" \
          "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/packages/generic/chrome-extension/${VERSION}/alienor-webhost-extension-${VERSION}.xpi"
      curl --header "JOB-TOKEN: $CI_JOB_TOKEN" \
          --upload-file "dist/alienor-webhost-extension-${VERSION}.xpi" \
          "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/packages/generic/chrome-extension/latest/alienor-webhost-extension.xpi"
  artifacts:
    paths:
      - dist/
    expire_in: 1 week
  only:
    changes:
      - chrome-extension/**/*
    refs:
      - master
      - develop
      - feature/chrome-extension

build-image-prod:
  stage: build-image
  needs: []
  image: docker:latest
  variables:
    DOCKER_TLS_CERTDIR: '/certs'
  before_script:
    - mkdir -p $HOME/.docker
    - echo $DOCKER_AUTH_CONFIG > $HOME/.docker/config.json
  script:
    - docker compose -f compose.yaml -f compose.prod.yaml build php
    - echo "Image build successfully"
    - docker compose -f compose.yaml -f compose.prod.yaml push php
    - echo "Image push successfully"
  only:
    - master

deploy-prod:
  image: gitlab.alienor.net:5050/dev-docker/docker-tools
  stage: deploy
  needs: ['build-image-prod']
  environment:
    name: production
    url: https://tdb-swarm.int.alienor.net
  variables:
    GIT_STRATEGY: none
  script:
    - docker-tools update anet-dev web-tdb-swarm_front_web -i gitlab.alienor.net:5050/dev-interne/tdb-docker-swarm:prod --detach
    - docker-tools update anet-dev web-tdb-swarm_scheduler -i gitlab.alienor.net:5050/dev-interne/tdb-docker-swarm:prod --detach
    - docker-tools update anet-dev web-tdb-swarm_consumer -i gitlab.alienor.net:5050/dev-interne/tdb-docker-swarm:prod --detach
  only:
    - master
