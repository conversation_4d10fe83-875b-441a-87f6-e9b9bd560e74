<?php

namespace App\Repository;

use App\Entity\WebHost\VirtualMachine;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<VirtualMachine>
 */
class VirtualMachineRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, VirtualMachine::class);
    }

    /**
     * Trouve toutes les VMs avec le nombre de configurations associées
     */
    public function findAllWithConfigurationCount(): array
    {
        return $this->createQueryBuilder('vm')
            ->leftJoin('vm.configurations', 'config')
            ->addSelect('config')
            ->orderBy('vm.name', 'ASC')
            ->addOrderBy('vm.ip', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les VMs disponibles (non utilisées ou réutilisables)
     */
    public function findAvailableVMs(): array
    {
        return $this->createQueryBuilder('vm')
            ->orderBy('vm.name', 'ASC')
            ->addOrderBy('vm.ip', 'ASC')
            ->getQuery()
            ->getResult();
    }
}
