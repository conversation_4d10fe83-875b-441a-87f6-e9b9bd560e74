<!doctype html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>WebHost Links</title>
		<style>
			* {
				box-sizing: border-box;
			}

			body {
				width: 380px;
				margin: 0;
				padding: 0;
				font-family:
					-apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
				font-size: 13px;
				line-height: 1.4;
				background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				color: #333;
			}

			.container {
				background: white;
				border-radius: 12px;
				margin: 8px;
				box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
				overflow: hidden;
			}

			.header {
				background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				color: white;
				padding: 16px;
				text-align: center;
			}

			.header h1 {
				margin: 0 0 8px 0;
				font-size: 18px;
				font-weight: 600;
				text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
			}

			.current-url {
				font-size: 11px;
				opacity: 0.9;
				word-break: break-all;
				background: rgba(255, 255, 255, 0.1);
				padding: 6px 10px;
				border-radius: 20px;
				margin-top: 8px;
			}

			.content {
				padding: 0;
			}

			/* Styles globaux pour les composants Lit */
			popup-app {
				display: block;
			}
		</style>
	</head>
	<body>
		<div class="container">
			<div class="header">
				<h1>WebHost Links</h1>
				<div class="current-url" id="currentUrl">Chargement...</div>
			</div>

			<div class="content">
				<popup-app></popup-app>
			</div>
		</div>

		<script src="config.js"></script>
		<script>
			// Fonction de debug globale
			window.debugLog = function(...args) {
				if (window.CONFIG && window.CONFIG.DEBUG) {
					console.log('[WebHost Links]', ...args);
				}
			};
		</script>
		<script type="module" src="components/popup-app.js"></script>
	</body>
</html>
