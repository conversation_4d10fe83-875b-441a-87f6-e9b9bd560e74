<?php

namespace App\Entity\WebHost\WebHostConfiguration;

use App\Entity\WebHost\VirtualMachine;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
class VirtualMachineConfiguration extends WebHostConfiguration
{
    #[ORM\ManyToOne(targetEntity: VirtualMachine::class, inversedBy: 'configurations')]
    #[ORM\JoinColumn(nullable: true)]
    private ?VirtualMachine $virtualMachine = null;

    #[ORM\Column(type: 'string', nullable: true)]
    private ?string $location = null;

    public function getVirtualMachine(): ?VirtualMachine
    {
        return $this->virtualMachine;
    }

    public function setVirtualMachine(?VirtualMachine $virtualMachine): static
    {
        $this->virtualMachine = $virtualMachine;
        return $this;
    }

    public function getIp(): ?string
    {
        return $this->virtualMachine?->getIp();
    }

    public function getLocation(): ?string
    {
        return $this->location;
    }

    public function setLocation(?string $location): void
    {
        $this->location = $location;
    }

    public function getWebIdFromLocation(): ?string
    {
        $location = $this->getLocation();
        if (str_contains($location, '/')) {
            $paths = explode('/', $location);

            return $paths[count($paths) - 1];
        }

        return null;
    }

    public function getType(): string
    {
        return self::TYPE_VM;
    }
}
