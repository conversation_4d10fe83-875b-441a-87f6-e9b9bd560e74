import { defineManifest } from '@crxjs/vite-plugin';
import pkg from './package.json';

const logo = process.env.NODE_ENV === 'development' ? 'public/logo-dev.png' : 'public/logo.png';
const browser = process.env.BROWSER || 'chrome';

// @ts-ignore
export default defineManifest({
	manifest_version: 3,
	name: 'Alienor WebHost Extension',
	description: "Affiche les liens utiles (GitLab, Confluence, Database) pour l'URL actuelle.",
	version: pkg.version,
	icons: {
		48: logo
	},
	action: {
		default_icon: {
			48: logo
		},
		default_popup: 'src/popup/index.html'
	},
	options_page: 'src/options/index.html',
	permissions: ['activeTab', 'storage', 'tabs'],
	background:
		browser === 'chrome'
			? {
					service_worker: 'src/service-worker.js'
				}
			: {
					scripts: ['src/service-worker.js']
				},
	host_permissions: [
		'http://localhost/*',
		'https://localhost/*',
		'https://tdb-swarm.int.alienor.net/*'
	],
	update_url:
		'https://gitlab.alienor.net/api/v4/projects/591/packages/generic/chrome-extension/latest/updates.xml',
	browser_specific_settings: {
		gecko: {
			id: pkg.name + '@alienor.net'
		}
	}
});
