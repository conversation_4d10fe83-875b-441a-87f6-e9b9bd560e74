// Utilitaires pour créer des templates HTML avec des template literals

export const templates = {
  webHostCard: (webHost) => {
    const gitInfo = (webHost.gitlabActiveBranch || webHost.lastCommitDate) ? 
      `<div class="git-info">
        ${webHost.gitlabActiveBranch ? `🌿 ${webHost.gitlabActiveBranch}` : ''}
        ${webHost.gitlabActiveBranch && webHost.lastCommitDate ? ' • ' : ''}
        ${webHost.lastCommitDate ? `📅 ${webHost.lastCommitDate}` : ''}
      </div>` : '';

    return `
      <div class="webhost-card">
        <div class="webhost-header">
          <div class="webhost-icon">
            ${(webHost.name || 'W').charAt(0).toUpperCase()}
          </div>
          <div class="webhost-info">
            <div class="webhost-name">
              ${webHost.name || 'Nom non défini'}
            </div>
            <div class="webhost-meta">
              ${webHost.environnement ? `<span class="meta-badge">${webHost.environnement}</span>` : ''}
              ${webHost.expectedVisibility ? `<span class="meta-badge">${webHost.expectedVisibility}</span>` : ''}
            </div>
          </div>
        </div>
        ${gitInfo}
      </div>
    `;
  },

  linkCard: (link) => {
    const icon = getLinkIcon(link.type);
    const linkText = getLinkText(link);
    const tooltip = getLinkTooltip(link);
    const statusIndicator = link.status ? createStatusIndicatorHTML(link.status) : '';

    return `
      <div class="link-card" data-url="${link.url}">
        <div class="link-main">
          <span class="link-icon">${icon}</span>
          <div class="link-info">
            <div class="link-title">${link.title}</div>
            <a class="link-url" href="${link.url}" target="_blank" title="${tooltip}">
              ${linkText}
            </a>
          </div>
        </div>
        ${statusIndicator}
      </div>
    `;
  },

  hostingInfo: (configuration) => {
    if (!configuration) return '';

    const items = [];
    
    if (configuration.type) {
      items.push(`
        <div class="hosting-item">
          <span class="hosting-label">Type:</span>
          <span class="hosting-value">${configuration.type.toUpperCase()}</span>
        </div>
      `);
    }

    if (configuration.webId) {
      items.push(`
        <div class="hosting-item">
          <span class="hosting-label">Web ID:</span>
          <span class="hosting-value">${configuration.webId}</span>
        </div>
      `);
    }

    if (configuration.server) {
      items.push(`
        <div class="hosting-item">
          <span class="hosting-label">Serveur:</span>
          <span class="hosting-value">${configuration.server}</span>
        </div>
      `);
    }

    return `
      <div class="hosting-info">
        <div class="hosting-title">🏠 Hébergement</div>
        <div class="hosting-details">
          ${items.join('')}
        </div>
      </div>
    `;
  },

  techInfo: (serviceDetails, serviceRepository) => {
    if (!serviceDetails && !serviceRepository) return '';

    const items = [];

    // PHP
    if (serviceDetails?.php) {
      const phpStatus = getPhpStatusHTML(serviceDetails.php);
      items.push(`
        <div class="tech-item">
          <span class="tech-label">PHP:</span>
          <span class="tech-value">${serviceDetails.php.version}</span>
          ${phpStatus}
        </div>
      `);
    }

    // Symfony
    if (serviceDetails?.symfony) {
      const symfonyStatus = getSymfonyStatusHTML(serviceDetails.symfony);
      items.push(`
        <div class="tech-item">
          <span class="tech-label">Symfony:</span>
          <span class="tech-value">${serviceDetails.symfony.version}</span>
          ${symfonyStatus}
        </div>
      `);
    }

    // Docker
    if (serviceRepository?.image) {
      items.push(`
        <div class="tech-item">
          <span class="tech-label">Docker:</span>
          <span class="tech-value" title="${serviceRepository.image}">
            ${serviceRepository.tag || 'N/A'}
          </span>
        </div>
      `);
    }

    if (items.length === 0) return '';

    return `
      <div class="tech-info">
        <div class="tech-title">⚙️ Technologies</div>
        <div class="tech-details">
          ${items.join('')}
        </div>
      </div>
    `;
  },

  error: (message) => `
    <div class="error">
      <strong>Erreur:</strong> ${message}
    </div>
  `,

  noMatch: (showClearCache = false) => `
    <div class="no-match">
      <div>Aucun webhost trouvé pour cette URL</div>
      ${showClearCache ? '<div style="margin-top: 16px;"><a href="#" class="clear-cache-link" id="clearCache">🗑️ Vider le cache</a></div>' : ''}
    </div>
  `
};

// Fonctions utilitaires
function getLinkIcon(type) {
  const icons = {
    gitlab: '🦊',
    confluence: '📖',
    database: '🗄️',
    url: '🌐',
    associated: '🔗',
    docker: '🐳'
  };
  return icons[type] || '🔗';
}

function getLinkText(link) {
  if (link.label) return link.label;
  if (link.type === 'docker') return 'Ouvrir avec Docker Tools';
  if (link.originalUrl && link.originalUrl !== link.url) return link.originalUrl;
  return link.url;
}

function getLinkTooltip(link) {
  if (link.type === 'docker') return `Clique pour ouvrir: ${link.url}`;
  if (link.originalUrl && link.originalUrl !== link.url) return `Clique pour ouvrir: ${link.url}`;
  return link.url;
}

function createStatusIndicatorHTML(status) {
  const statusClass = status === 'OK' ? 'status-ok' : 
                     status === 'ERROR' || status === 'TIMEOUT' ? 'status-error' : 
                     'status-unknown';
  
  const title = status === 'OK' ? 'Service disponible' :
                status === 'ERROR' || status === 'TIMEOUT' ? 'Service indisponible' :
                'Statut inconnu';

  return `<span class="status-indicator ${statusClass}" title="${title}"></span>`;
}

function getPhpStatusHTML(php) {
  if (php.is_eoled) {
    return '<span class="tech-status eol" title="Version en fin de vie">EOL</span>';
  } else if (!php.is_latest) {
    return `<span class="tech-status outdated" title="Dernière version: ${php.latest_patch_version}">Ancienne</span>`;
  } else {
    return '<span class="tech-status current">À jour</span>';
  }
}

function getSymfonyStatusHTML(symfony) {
  if (symfony.is_eoled) {
    return `<span class="tech-status eol" title="Fin de vie: ${symfony.eol}">EOL</span>`;
  } else if (symfony.is_eomed) {
    return `<span class="tech-status outdated" title="Fin de maintenance: ${symfony.eom}">EOM</span>`;
  } else if (!symfony.is_latest) {
    return `<span class="tech-status outdated" title="Dernière version: ${symfony.latest_patch_version}">Ancienne</span>`;
  } else {
    return '<span class="tech-status current">À jour</span>';
  }
}
