<?php

namespace App\Entity\WebHost;

use App\Entity\WebHost\WebHostConfiguration\VirtualMachineConfiguration;
use App\Repository\VirtualMachineRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: VirtualMachineRepository::class)]
class VirtualMachine
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: 'string', unique: true)]
    private ?string $ip = null;

    #[ORM\Column(type: 'string', nullable: true)]
    private ?string $phpVersion = null;

    #[ORM\Column(type: 'string', nullable: true)]
    private ?string $name = null;

    #[ORM\OneToMany(mappedBy: 'virtualMachine', targetEntity: VirtualMachineConfiguration::class)]
    private Collection $configurations;

    public function __construct()
    {
        $this->configurations = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getIp(): ?string
    {
        return $this->ip;
    }

    public function setIp(?string $ip): static
    {
        $this->ip = $ip;
        return $this;
    }

    public function getPhpVersion(): ?string
    {
        return $this->phpVersion;
    }

    public function setPhpVersion(?string $phpVersion): static
    {
        $this->phpVersion = $phpVersion;
        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): static
    {
        $this->name = $name;
        return $this;
    }

    /**
     * @return Collection<int, VirtualMachineConfiguration>
     */
    public function getConfigurations(): Collection
    {
        return $this->configurations;
    }

    public function addConfiguration(VirtualMachineConfiguration $configuration): static
    {
        if (!$this->configurations->contains($configuration)) {
            $this->configurations->add($configuration);
            $configuration->setVirtualMachine($this);
        }

        return $this;
    }

    public function removeConfiguration(VirtualMachineConfiguration $configuration): static
    {
        if ($this->configurations->removeElement($configuration)) {
            // set the owning side to null (unless already changed)
            if ($configuration->getVirtualMachine() === $this) {
                $configuration->setVirtualMachine(null);
            }
        }

        return $this;
    }

    public function __toString(): string
    {
        return $this->name ? sprintf('%s (%s)', $this->name, $this->ip) : (string) $this->ip;
    }
}
