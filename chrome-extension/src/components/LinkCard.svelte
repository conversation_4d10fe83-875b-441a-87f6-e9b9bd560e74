<script lang="ts">
	import type { Link } from '@/popup/types.js';
	import { getLinkIcon } from '@/popup/utils.js';

	let { link }: { link: Link } = $props();

	function handleClick(event: MouseEvent) {
		// Si on clique sur le lien lui-même, laisser le comportement par défaut
		if ((event.target as HTMLElement).tagName === 'A') {
			return;
		}
		// Sinon, ouvrir le lien dans un nouvel onglet
		window.open(link.url, '_blank');
	}

	function createStatusIndicator(status: string) {
		if (status === 'OK') {
			return { class: 'status-ok', title: 'Service disponible' };
		} else if (status === 'ERROR' || status === 'TIMEOUT') {
			return { class: 'status-error', title: 'Service indisponible' };
		} else {
			return { class: 'status-unknown', title: 'Statut inconnu' };
		}
	}

	let statusInfo = $derived(link.status ? createStatusIndicator(link.status) : null);

	let displayText = $derived.by(() => {
		if (link.type === 'docker') {
			return 'Ouvrir avec Docker Tools';
		} else if (link.originalUrl && link.originalUrl !== link.url) {
			return link.originalUrl;
		} else {
			return link.url;
		}
	});

	let linkTitle = $derived.by(() => {
		if (link.type === 'docker') {
			return `Clique pour ouvrir: ${link.url}`;
		} else if (link.originalUrl && link.originalUrl !== link.url) {
			return `Clique pour ouvrir: ${link.url}`;
		} else {
			return link.url;
		}
	});
</script>

<!-- svelte-ignore a11y_click_events_have_key_events -->
<!-- svelte-ignore a11y_no_static_element_interactions -->
<div class="link-card" onclick={handleClick}>
	<div class="link-main">
		<span class="link-icon">{getLinkIcon(link.type)}</span>
		<div class="link-info">
			<div class="link-title">{link.title}</div>
			<a class="link-url" href={link.url} target="_blank" title={linkTitle}>
				{link.label || displayText}
			</a>
		</div>
	</div>

	{#if statusInfo}
		<span class="status-indicator {statusInfo.class}" title={statusInfo.title}></span>
	{/if}
</div>

<style>
	.link-card {
		background: white;
		border: 1px solid #e9ecef;
		border-radius: 6px;
		padding: 8px 10px;
		transition: all 0.2s ease;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: space-between;
		overflow: hidden;
	}

	.link-card:hover {
		border-color: #667eea;
		box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
		transform: translateY(-1px);
	}

	.link-main {
		display: flex;
		align-items: center;
		flex: 1;
		min-width: 0;
	}

	.link-icon {
		font-size: 14px;
		margin-right: 8px;
		width: 16px;
		text-align: center;
		flex-shrink: 0;
	}

	.link-info {
		flex: 1;
		min-width: 0;
	}

	.link-title {
		font-weight: 500;
		color: #333;
		font-size: 12px;
		margin-bottom: 1px;
	}

	.link-url {
		color: #667eea;
		text-decoration: none;
		font-size: 10px;
		word-break: break-all;
		display: block;
		opacity: 0.8;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.link-url:hover {
		text-decoration: underline;
	}

	.status-indicator {
		width: 6px;
		height: 6px;
		border-radius: 50%;
		flex-shrink: 0;
		margin-left: 8px;
	}

	.status-ok {
		background-color: #28a745;
		box-shadow: 0 0 4px rgba(40, 167, 69, 0.4);
	}

	.status-error {
		background-color: #dc3545;
		box-shadow: 0 0 4px rgba(220, 53, 69, 0.4);
	}

	.status-unknown {
		background-color: #6c757d;
	}
</style>
