import { LitElement, html, css } from 'lit';

export class TechInfo extends LitElement {
  static properties = {
    serviceDetails: { type: Object },
    serviceRepository: { type: Object }
  };

  static styles = css`
    .tech-info {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 16px;
    }

    .tech-title {
      font-size: 12px;
      font-weight: 600;
      color: #495057;
      margin-bottom: 8px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .tech-details {
      display: grid;
      gap: 6px;
    }

    .tech-item {
      display: flex;
      align-items: center;
      font-size: 11px;
    }

    .tech-label {
      font-weight: 500;
      color: #6c757d;
      min-width: 80px;
      margin-right: 8px;
    }

    .tech-value {
      color: #495057;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      background: #e9ecef;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 10px;
    }

    .tech-status {
      margin-left: 6px;
      font-size: 9px;
      padding: 1px 4px;
      border-radius: 3px;
      font-weight: 500;
    }

    .tech-status.outdated {
      background: #fff3cd;
      color: #856404;
    }

    .tech-status.eol {
      background: #f8d7da;
      color: #721c24;
    }

    .tech-status.current {
      background: #d4edda;
      color: #155724;
    }
  `;

  render() {
    if (!this.serviceDetails && !this.serviceRepository) return html``;

    const items = this._getTechItems();
    if (items.length === 0) return html``;

    return html`
      <div class="tech-info">
        <div class="tech-title">⚙️ Technologies</div>
        <div class="tech-details">
          ${items}
        </div>
      </div>
    `;
  }

  _getTechItems() {
    const items = [];

    // PHP
    if (this.serviceDetails?.php) {
      items.push(html`
        <div class="tech-item">
          <span class="tech-label">PHP:</span>
          <span class="tech-value">${this.serviceDetails.php.version}</span>
          ${this._renderPhpStatus(this.serviceDetails.php)}
        </div>
      `);
    }

    // Symfony
    if (this.serviceDetails?.symfony) {
      items.push(html`
        <div class="tech-item">
          <span class="tech-label">Symfony:</span>
          <span class="tech-value">${this.serviceDetails.symfony.version}</span>
          ${this._renderSymfonyStatus(this.serviceDetails.symfony)}
        </div>
      `);
    }

    // Docker
    if (this.serviceRepository?.image) {
      items.push(html`
        <div class="tech-item">
          <span class="tech-label">Docker:</span>
          <span class="tech-value" title="${this.serviceRepository.image}">
            ${this.serviceRepository.tag || 'N/A'}
          </span>
        </div>
      `);
    }

    return items;
  }

  _renderPhpStatus(php) {
    if (php.is_eoled) {
      return html`<span class="tech-status eol" title="Version en fin de vie">EOL</span>`;
    } else if (!php.is_latest) {
      return html`<span class="tech-status outdated" title="Dernière version: ${php.latest_patch_version}">Ancienne</span>`;
    } else {
      return html`<span class="tech-status current">À jour</span>`;
    }
  }

  _renderSymfonyStatus(symfony) {
    if (symfony.is_eoled) {
      return html`<span class="tech-status eol" title="Fin de vie: ${symfony.eol}">EOL</span>`;
    } else if (symfony.is_eomed) {
      return html`<span class="tech-status outdated" title="Fin de maintenance: ${symfony.eom}">EOM</span>`;
    } else if (!symfony.is_latest) {
      return html`<span class="tech-status outdated" title="Dernière version: ${symfony.latest_patch_version}">Ancienne</span>`;
    } else {
      return html`<span class="tech-status current">À jour</span>`;
    }
  }
}

customElements.define('tech-info', TechInfo);
