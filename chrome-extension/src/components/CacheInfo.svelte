<script lang="ts">
	import { CONFIG, debugLog } from '@/popup/config.js';

	async function clearCache() {
		try {
			await chrome.runtime.sendMessage({ action: 'clearCache' });
			debugLog('Cache vidé, rechargement...');
			window.location.reload();
		} catch (error) {
			debugLog('Erreur lors du vidage du cache:', error);
		}
	}
</script>

{#if CONFIG.DEBUG}
	<div class="cache-info">
		💾 Données mises en cache (1h) •
		<button class="clear-cache-link" onclick={clearCache}> Vider le cache </button>
	</div>
{/if}

<style>
	.cache-info {
		font-size: 10px;
		color: #999;
		text-align: center;
		padding: 8px;
		background: #f8f9fa;
		border-top: 1px solid #e9ecef;
		margin: 0 -16px -16px -16px;
	}

	.clear-cache-link {
		color: #667eea;
		text-decoration: none;
		font-weight: 500;
		background: none;
		border: none;
		cursor: pointer;
		font-size: inherit;
	}

	.clear-cache-link:hover {
		text-decoration: underline;
	}
</style>
