<script lang="ts">
	import type { WebHost, Link } from '@/popup/types.js';
	import type { ExtensionSettings } from '@/services/settings.js';
	import { convertGitUrlToHttp } from '@/popup/utils.js';
	import HostingInfo from './HostingInfo.svelte';
	import TechInfo from './TechInfo.svelte';
	import LinkCard from './LinkCard.svelte';
	import CacheInfo from './CacheInfo.svelte';
	import { setContext } from 'svelte';

	let { webHost, settings }: { webHost: WebHost; settings: ExtensionSettings } = $props();

	setContext('webHost', webHost);
	setContext('settings', settings);

	// Calculer l'icône du webhost (première lettre du nom)
	let webhostIcon = $derived((webHost.name || 'W').charAt(0).toUpperCase());

	// Collecter tous les liens
	let links = $derived.by(() => {
		const linksList: Link[] = [];

		// GitLab
		if (webHost.gitlabRemoteUrl) {
			const gitlabUrl = convertGitUrlToHttp(webHost.gitlabRemoteUrl);
			linksList.push({
				type: 'gitlab',
				title: 'GitLab',
				url: gitlabUrl,
				originalUrl: webHost.gitlabRemoteUrl
			});
		}

		// Confluence
		if (webHost.confluenceUrl) {
			linksList.push({
				type: 'confluence',
				title: 'Confluence',
				url: webHost.confluenceUrl
			});
		}

		// Base de données
		const databaseUrl = webHost.databaseUrl || webHost.databaseExplorerUrl;
		if (databaseUrl) {
			linksList.push({
				type: 'database',
				title: 'Base de données',
				url: databaseUrl
			});
		}

		// URLs associées
		if (webHost.associatedUrls && webHost.associatedUrls.length > 0) {
			webHost.associatedUrls.forEach((associatedUrl) => {
				linksList.push({
					type: 'associated',
					title: 'Lien associé',
					url: associatedUrl.url
				});
			});
		}

		// Docker Tools Command (depuis le service) - seulement si activé dans les paramètres
		if (
			settings.dockerToolsEnabled &&
			webHost.service &&
			webHost.service.tasks &&
			webHost.service.tasks.length > 0
		) {
			const firstTask = webHost.service.tasks[0];
			if (firstTask.dockerToolsCommand) {
				linksList.push({
					type: 'docker',
					title: 'Docker Tools',
					url: firstTask.dockerToolsCommand
				});
			}
		}

		// TDB Swarm - seulement si Docker Tools est activé
		if (webHost.service) {
			if (webHost.service.links?.logs) {
				linksList.push({
					type: 'docker',
					title: 'Logs Docker',
					label: 'Ouvrir dans Swarmpit',
					url: webHost.service.links.logs
				});
			}
		}

		return linksList;
	});

	// Calculer les informations Git
	let gitInfo = $derived.by(() => {
		if (!webHost.gitlabActiveBranch && !webHost.lastCommitDate) {
			return null;
		}

		let gitText = '';
		if (webHost.gitlabActiveBranch) {
			gitText += `🌿 ${webHost.gitlabActiveBranch}`;
		}
		if (webHost.lastCommitDate) {
			if (gitText) gitText += ' <br>';
			gitText += `📅 ${webHost.lastCommitDate}`;
		}

		return gitText;
	});
</script>

<div class="webhost-card">
	<div class="webhost-header">
		<div class="webhost-icon">{webhostIcon}</div>
		<div class="webhost-info">
			<div class="webhost-name">{webHost.name || 'Nom non défini'}</div>
			<div class="webhost-meta">
				{#if webHost.environnement}
					<span class="meta-badge">{webHost.environnement}</span>
				{/if}
				{#if webHost.expectedVisibility}
					<span class="meta-badge">{webHost.expectedVisibility}</span>
				{/if}
			</div>
		</div>
		<div class="webhost-edit">
			<a
				style="text-decoration: none;"
				href="https://tdb-swarm.int.alienor.net/webHosts/edit/{webHost.id}"
				target="_blank"
				title="Modifier le webhost">✏️</a
			>
		</div>
	</div>

	{#if gitInfo}
		<div class="git-info">{@html gitInfo}</div>
	{/if}
</div>

{#if webHost.configuration}
	<HostingInfo configuration={webHost.configuration} service={webHost.service} />
{/if}

{#if webHost.service}
	<TechInfo
		serviceDetails={webHost.service.details}
		serviceRepository={webHost.service.repository}
	/>
{/if}

<div class="links-grid">
	{#each links as link (link.url)}
		<LinkCard {link} />
	{/each}
</div>

<CacheInfo />

<style>
	.webhost-card {
		background: linear-gradient(135deg, #f8f9fa, #e9ecef);
		border-radius: 10px;
		padding: 16px;
		margin-bottom: 16px;
		border: 1px solid #dee2e6;
	}

	.webhost-header {
		display: flex;
		align-items: center;
		margin-bottom: 8px;
	}

	.webhost-icon {
		width: 32px;
		height: 32px;
		background: linear-gradient(135deg, #667eea, #764ba2);
		border-radius: 8px;
		display: flex;
		align-items: center;
		justify-content: center;
		color: white;
		font-weight: bold;
		margin-right: 12px;
		font-size: 14px;
	}

	.webhost-info {
		flex: 1;
	}

	.webhost-name {
		font-weight: 600;
		color: #333;
		margin: 0 0 2px 0;
		font-size: 15px;
	}

	.webhost-meta {
		font-size: 11px;
		color: #666;
		display: flex;
		gap: 8px;
		flex-wrap: wrap;
	}

	.meta-badge {
		background: #e9ecef;
		padding: 2px 6px;
		border-radius: 10px;
		text-transform: uppercase;
		font-weight: 500;
	}

	.git-info {
		font-size: 11px;
		color: #666;
		margin-top: 8px;
		padding-top: 8px;
		border-top: 1px solid #dee2e6;
	}

	.links-grid {
		display: grid;
		gap: 4px;
		margin-bottom: 16px;
	}
</style>
