import { LitElement, html, css } from 'lit';

export class WebHostCard extends LitElement {
  static properties = {
    webHost: { type: Object }
  };

  static styles = css`
    .webhost-card {
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);
      border-radius: 10px;
      padding: 16px;
      margin-bottom: 16px;
      border: 1px solid #dee2e6;
    }

    .webhost-header {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
    }

    .webhost-icon {
      width: 32px;
      height: 32px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      margin-right: 12px;
      font-size: 14px;
    }

    .webhost-info {
      flex: 1;
    }

    .webhost-name {
      font-weight: 600;
      color: #333;
      margin: 0 0 2px 0;
      font-size: 15px;
    }

    .webhost-meta {
      font-size: 11px;
      color: #666;
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .meta-badge {
      background: #e9ecef;
      padding: 2px 6px;
      border-radius: 10px;
      text-transform: uppercase;
      font-weight: 500;
    }

    .git-info {
      font-size: 11px;
      color: #666;
      margin-top: 8px;
      padding-top: 8px;
      border-top: 1px solid #dee2e6;
    }
  `;

  render() {
    if (!this.webHost) return html``;

    return html`
      <div class="webhost-card">
        <div class="webhost-header">
          <div class="webhost-icon">
            ${(this.webHost.name || 'W').charAt(0).toUpperCase()}
          </div>
          <div class="webhost-info">
            <div class="webhost-name">
              ${this.webHost.name || 'Nom non défini'}
            </div>
            <div class="webhost-meta">
              ${this.webHost.environnement ? 
                html`<span class="meta-badge">${this.webHost.environnement}</span>` : ''}
              ${this.webHost.expectedVisibility ? 
                html`<span class="meta-badge">${this.webHost.expectedVisibility}</span>` : ''}
            </div>
          </div>
        </div>
        
        ${this._renderGitInfo()}
      </div>
    `;
  }

  _renderGitInfo() {
    if (!this.webHost.gitlabActiveBranch && !this.webHost.lastCommitDate) {
      return '';
    }

    let gitText = '';
    if (this.webHost.gitlabActiveBranch) {
      gitText += `🌿 ${this.webHost.gitlabActiveBranch}`;
    }
    if (this.webHost.lastCommitDate) {
      if (gitText) gitText += ' • ';
      gitText += `📅 ${this.webHost.lastCommitDate}`;
    }

    return html`<div class="git-info">${gitText}</div>`;
  }
}

customElements.define('webhost-card', WebHostCard);
