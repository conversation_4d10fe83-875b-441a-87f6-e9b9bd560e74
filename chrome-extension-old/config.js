// Configuration de l'extension WebHost Links
const CONFIG = {
	// URL de base de votre API (sans le slash final)
	API_BASE_URL: 'https://tdb-swarm.int.alienor.net/',

	// Endpoint pour récupérer les données des webhosts
	API_ENDPOINT: '/api/webHosts/data',

	// Timeout pour les requêtes API (en millisecondes)
	API_TIMEOUT: 10000,

	// Activer les logs de debug dans la console
	DEBUG: true
};

// Fonction pour logger uniquement si le debug est activé
function debugLog(...args) {
	if (CONFIG.DEBUG) {
		console.log('[WebHost Links]', ...args);
	}
}

// Exporter la configuration
window.CONFIG = CONFIG;
window.debugLog = debugLog;
