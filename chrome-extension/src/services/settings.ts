// Service de gestion des paramètres de l'extension
export interface ExtensionSettings {
	dockerToolsEnabled: boolean;
}

// Paramètres par défaut
export const DEFAULT_SETTINGS: ExtensionSettings = {
	dockerToolsEnabled: false
};

// Clé de stockage pour les paramètres
const SETTINGS_STORAGE_KEY = 'extension_settings';

/**
 * Récupère les paramètres depuis le stockage Chrome
 */
export async function getSettings(): Promise<ExtensionSettings> {
	try {
		const result = await chrome.storage.sync.get([SETTINGS_STORAGE_KEY]);
		const settings = result[SETTINGS_STORAGE_KEY];

		// Fusionner avec les paramètres par défaut pour s'assurer que tous les champs sont présents
		return {
			...DEFAULT_SETTINGS,
			...settings
		};
	} catch (error) {
		console.error('Erreur lors de la récupération des paramètres:', error);
		return DEFAULT_SETTINGS;
	}
}

/**
 * Sauvegarde les paramètres dans le stockage Chrome
 */
export async function saveSettings(settings: ExtensionSettings): Promise<void> {
	try {
		await chrome.storage.sync.set({
			[SETTINGS_STORAGE_KEY]: settings
		});
	} catch (error) {
		console.error('Erreur lors de la sauvegarde des paramètres:', error);
		throw error;
	}
}

/**
 * Réinitialise les paramètres aux valeurs par défaut
 */
export async function resetSettings(): Promise<void> {
	await saveSettings(DEFAULT_SETTINGS);
}

/**
 * Écoute les changements de paramètres
 */
export function onSettingsChanged(callback: (settings: ExtensionSettings) => void): void {
	chrome.storage.onChanged.addListener((changes, areaName) => {
		if (areaName === 'sync' && changes[SETTINGS_STORAGE_KEY]) {
			const newSettings = {
				...DEFAULT_SETTINGS,
				...changes[SETTINGS_STORAGE_KEY].newValue
			};
			callback(newSettings);
		}
	});
}
