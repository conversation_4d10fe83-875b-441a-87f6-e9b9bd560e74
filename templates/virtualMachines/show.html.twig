{% extends 'front.html.twig' %}

{% block title %}{{ virtualMachine.name ?? virtualMachine.ip }}{% endblock %}

{% block body %}
    <div class="container-sm pt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>{{ virtualMachine.name ?? virtualMachine.ip }}</h2>
            <div class="btn-group">
                <a href="{{ path('app_virtual_machines_edit', {id: virtualMachine.id}) }}" class="btn btn-primary">
                    <i class="fas fa-edit"></i> Modifier
                </a>
                <a href="{{ path('app_virtual_machines_index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Retour à la liste
                </a>
            </div>
        </div>

        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Informations de la VM</h5>
                    </div>
                    <div class="card-body">
                        <dl class="row">
                            <dt class="col-sm-3">Nom :</dt>
                            <dd class="col-sm-9">
                                {% if virtualMachine.name %}
                                    {{ virtualMachine.name }}
                                {% else %}
                                    <em class="text-muted">Non défini</em>
                                {% endif %}
                            </dd>

                            <dt class="col-sm-3">Adresse IP :</dt>
                            <dd class="col-sm-9">
                                <code>{{ virtualMachine.ip }}</code>
                            </dd>

                            <dt class="col-sm-3">Version PHP :</dt>
                            <dd class="col-sm-9">
                                {% if virtualMachine.phpVersion %}
                                    <span class="badge bg-info">PHP {{ virtualMachine.phpVersion }}</span>
                                {% else %}
                                    <em class="text-muted">Non définie</em>
                                {% endif %}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Configurations liées</h5>
                    </div>
                    <div class="card-body">
                        {% if virtualMachine.configurations|length > 0 %}
                            <p class="text-muted mb-2">Cette VM est utilisée par {{ virtualMachine.configurations|length }} configuration(s) :</p>
                            <ul class="list-unstyled">
                                {% for config in virtualMachine.configurations %}
                                    <li class="mb-2">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-server text-muted me-2"></i>
                                            <div>
                                                <strong>WebID:</strong> {{ config.webId ?? 'Non défini' }}<br>
                                                <small class="text-muted">Location: {{ config.location ?? 'Non définie' }}</small>
                                            </div>
                                        </div>
                                    </li>
                                {% endfor %}
                            </ul>
                        {% else %}
                            <p class="text-muted">Cette VM n'est utilisée par aucune configuration.</p>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                Vous pouvez supprimer cette VM en toute sécurité.
                            </div>
                        {% endif %}
                    </div>
                </div>

                {% if virtualMachine.configurations|length == 0 %}
                    <div class="card mt-3">
                        <div class="card-header bg-danger text-white">
                            <h5 class="card-title mb-0">Zone de danger</h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">Supprimer définitivement cette machine virtuelle.</p>
                            <form method="post" action="{{ path('app_virtual_machines_delete', {id: virtualMachine.id}) }}" 
                                  onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cette VM ? Cette action est irréversible.')">
                                <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ virtualMachine.id) }}">
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash"></i> Supprimer la VM
                                </button>
                            </form>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
{% endblock %}
