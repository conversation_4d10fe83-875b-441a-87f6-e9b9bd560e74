<script lang="ts">
	import { CONFIG } from '@/popup/config.js';
	import { getCurrentTabUrl } from '@/popup/utils.js';
	import { onMount } from 'svelte';

	async function clearCache() {
		try {
			await chrome.runtime.sendMessage({ action: 'clearCache' });
			window.location.reload();
		} catch (error) {
			console.error('Erreur lors du vidage du cache:', error);
		}
	}

	let currentUrl = null;

	onMount(async () => {
		currentUrl = await getCurrentTabUrl();
	});
</script>

<div class="no-match">
	<div>Aucun webhost trouvé pour cette URL</div>
	{#if CONFIG.DEBUG}
		<div style="margin-top: 16px;">
			<button class="clear-cache-link" onclick={clearCache}> 🗑️ Vider le cache </button>
		</div>
	{/if}
	<div style="margin-top: 16px;">
		<a
			href="https://tdb-swarm.int.alienor.net/webHosts/create?url={currentUrl}"
			target="_blank"
			class="clear-cache-link"
		>
			➕ Ajouter un webhost
		</a>
	</div>
</div>

<style>
	.no-match {
		text-align: center;
		padding: 32px 16px;
		color: #666;
	}

	.no-match::before {
		content: '🔍';
		display: block;
		font-size: 32px;
		margin-bottom: 12px;
		opacity: 0.5;
	}

	.clear-cache-link {
		color: #667eea;
		text-decoration: none;
		font-weight: 500;
		background: none;
		border: none;
		cursor: pointer;
		font-size: inherit;
	}

	.clear-cache-link:hover {
		text-decoration: underline;
	}
</style>
