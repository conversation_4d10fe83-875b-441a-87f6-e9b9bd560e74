<script lang="ts">
	import { triggerWebHostChecks } from '../../../api';

	interface Props {
		webHost: WebHost;
		showDetails?: any;
	}

	let { webHost, showDetails = (service) => {} }: Props = $props();
	let isRefreshing = $state(false);

	const confirmDelete = () => {
		return confirm('Voulez-vous vraiment supprimer cet hébergement ?');
	};

	const refreshChecks = async () => {
		if (isRefreshing) return;

		isRefreshing = true;
		try {
			await triggerWebHostChecks(webHost.id);
		} catch (error) {
			console.error('Erreur lors du déclenchement des vérifications:', error);
		} finally {
			isRefreshing = false;
		}
	};
</script>

<div class="d-flex gap-1">
	<div class="text-nowrap text-end">
		<button
			class="btn btn-sm btn-info text-white"
			class:btn-info={(null !== webHost?.service && !webHost?.service?.redeployedAllowed) ||
				(null !== webHost?.service &&
					webHost?.service.redeployedAllowed &&
					webHost.service.details !== null)}
			class:btn-secondary={null === webHost?.service ||
				(webHost?.service?.redeployedAllowed && webHost?.service?.details === null)}
			disabled={null === webHost?.service}
			onclick={() => showDetails(webHost?.service)}><i class="fa fa-info-circle"></i></button
		>
		<button
			onclick={refreshChecks}
			class="btn btn-sm btn-info text-white"
			disabled={isRefreshing}
			title="Relancer les vérifications (Healthcheck & SSL)"
		>
			{#if isRefreshing}
				<i class="fa-solid fa-circle-notch fa-spin"></i>
			{:else}
				<i class="fa-solid fa-arrows-rotate"></i>
			{/if}
		</button>
		<a href="/webHosts/edit/{webHost.id}" class="btn btn-sm btn-info text-white" title="Modifier">
			<i class="fa-regular fa-pen"></i>
		</a>
		<a
			href="/webHosts/delete/{webHost.id}"
			class="btn btn-sm btn-danger text-white"
			onclick={confirmDelete}
			title="Supprimer"
		>
			<i class="fa-solid fa-trash-can"></i>
		</a>
	</div>
</div>
