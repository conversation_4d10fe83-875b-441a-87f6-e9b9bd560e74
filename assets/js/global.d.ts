export {};

declare global {
	type Task = {
		id: string;
		nodeName: string;
		state: 'running' | 'shutdown';
		stats: {
			memoryLimit: number;
			memory: number;
			cpuPercentage: number;
			cpuLimit: number;
			memoryPercentage: number;
			cpu: number;
		};
		tooltip: string;
		sshCommand: string;
		dockerToolsCommand: string;
	};

	type Service = {
		id: string;
		name: string;
		stack: string;
		server: string;
		env: string;
		middleware?: string;
		updatedAt: string;
		urls: string[];
		status: {
			tasks: {
				running: number;
				total: number;
			};
			update: string;
			message: string;
		};
		repository: {
			name: string;
			tag: string;
			image: string;
			imageDigest: string;
		};
		resources: {
			reservation: {
				cpu: number;
				memory: number;
			};
			limit: {
				cpu: number;
				memory: number;
			};
		};
		tasks: Task[];
		redeployedAllowed: boolean;
		details: Details;
		variables: { [key: string]: string };
		links: {
			logs?: string;
			service?: string;
			phpinfo?: string;
		};
	};

	export interface ComposerDependency {
		name: string;
		'direct-dependency': boolean;
		homepage: string;
		source: string;
		version: string;
		description: string;
		abandoned: boolean;
	}

	export interface DetailsComposer {
		installed: ComposerDependency[];
	}

	export interface DetailsSymfony {
		version: string;
		latest_patch_version: string;
		is_latest: boolean;
		is_lts: boolean;
		is_eomed: boolean;
		is_eoled: boolean;
		eom: string;
		eol: string;
	}

	export interface DetailsWordpress {
		version: string;
	}

	export interface DetailsPhp {
		version: string;
		buildDate: string;
		is_eoled: boolean;
		is_latest: boolean;
		latest_patch_version: string;
	}

	export interface DetailsEnv {
		[key: string]: string;
	}

	export interface DetailsGit {
		url: string;
		branch: string;
		tag?: string;
		history: string;
		lastCommit?: string;
	}

	export interface DetailsPortail {
		webserviceUrl: string;
	}

	export interface DetailsHtaccess {
		exists: boolean;
		requireUser: ?string[];
	}

	export interface DetailsSecurity {
		[key: string]: {
			advisories: {
				cve: string;
				link: string;
				title: string;
			};
		}[];
	}

	export interface DetailsSonarQube {
		projectKey: string;
		url: string;
		badges: {
			alert_status: string;
			bugs: string;
			code_smells: string;
			coverage: string;
			duplicated_lines_density: string;
			ncloc: string;
			reliability_rating: string;
			security_hotspots: string;
			security_rating: string;
			sqale_index: string;
			sqale_rating: string;
			vulnerabilities: string;
		};
	}

	export interface Details {
		date: string;
		composer: DetailsComposer;
		'local-php-security-checker': DetailsSecurity;
		symfony: DetailsSymfony;
		wordpress: DetailsWordpress;
		env: DetailsEnv;
		git: DetailsGit;
		portail: DetailsPortail;
		php: DetailsPhp;
		htaccess: DetailsHtaccess;
		sonarQube: DetailsSonarQube;
		timing: number;
	}

	/*** WebHost Types ***/

	type WebHostUrl = {
		id: number;
		url: string;
		healthCheckReport: {
			status?: string;
		};
		sslCertificateReport: {
			isValid?: boolean;
		};
	};

	type WebHostConfiguration = {
		type: string;
		stack?: string;
		webId?: string;
	};

	type WebHostAssociatedUrl = {
		url: string;
		favicon?: string;
	};

	type WebHost = {
		id: number;
		name: string;
		environnement?: string;
		expectedVisibility?: string;
		gitlabRemoteUrl?: string;
		gitlabActiveBranch?: string;
		lastCommitDate?: string;
		configuration?: WebHostConfiguration;
		urls: WebHostUrl[];
		confluenceUrl: string;
		databaseUrl: string;
		associatedUrls: WebHostAssociatedUrl[];
		service: Service;
	};

	/*** DTO ***/

	type WaitingService = {
		type: 'refresh' | 'redeploy';
		serverName: string;
		serviceId: string;
		error?: string;
		waiting: boolean;
	};

	type WaitingWebHost = {
		type: 'refresh';
		webHostId: number;
		error?: string;
		waiting: boolean;
	};
}

import '@tanstack/svelte-table';

declare module '@tanstack/svelte-table' {
	interface ColumnMeta<TData extends RowData, TValue> {
		cellHTML: boolean;
	}
}
