<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250923145400 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE virtual_machine (id SERIAL NOT NULL, ip VARCHAR(255) NOT NULL, php_version VARCHAR(255) DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_23720C4BA5E3B32D ON virtual_machine (ip)');
        $this->addSql('ALTER TABLE web_host_configuration ADD virtual_machine_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE web_host_configuration DROP ip');
        $this->addSql('ALTER TABLE web_host_configuration ADD CONSTRAINT FK_D4B97A09830F82E FOREIGN KEY (virtual_machine_id) REFERENCES virtual_machine (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_D4B97A09830F82E ON web_host_configuration (virtual_machine_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE web_host_configuration DROP CONSTRAINT FK_D4B97A09830F82E');
        $this->addSql('DROP TABLE virtual_machine');
        $this->addSql('DROP INDEX IDX_D4B97A09830F82E');
        $this->addSql('ALTER TABLE web_host_configuration ADD ip VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE web_host_configuration DROP virtual_machine_id');
    }
}
