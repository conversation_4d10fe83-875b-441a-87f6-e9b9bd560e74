export interface WebHostUrl {
	url: string;
	status?: string;
}

export interface AssociatedUrl {
	url: string;
}

export interface Configuration {
	type?: string;
	webId?: string;
	server?: string;
}

export interface ServiceDetails {
	php?: {
		version: string;
		is_eoled: boolean;
		is_latest: boolean;
		latest_patch_version?: string;
	};
	symfony?: {
		version: string;
		is_eoled: boolean;
		is_eomed: boolean;
		is_latest: boolean;
		latest_patch_version?: string;
		eol?: string;
		eom?: string;
	};
}

export interface ServiceRepository {
	image?: string;
	name?: string;
	tag?: string;
}

export interface ServiceTask {
	dockerToolsCommand?: string;
}

export interface ServiceLinks {
	logs?: string;
}

export interface Service {
	id: string;
	details?: ServiceDetails;
	repository?: ServiceRepository;
	tasks?: ServiceTask[];
	links?: ServiceLinks;
}

export interface WebHost {
	name: string;
	environnement?: string;
	expectedVisibility?: string;
	urls?: WebHostUrl[];
	associatedUrls?: AssociatedUrl[];
	gitlabRemoteUrl?: string;
	gitlabActiveBranch?: string;
	lastCommitDate?: string;
	confluenceUrl?: string;
	databaseUrl?: string;
	databaseExplorerUrl?: string;
	configuration?: Configuration;
	service?: Service;
}

export interface Link {
	type: 'gitlab' | 'confluence' | 'database' | 'url' | 'associated' | 'docker';
	title: string;
	url: string;
	originalUrl?: string;
	label?: string;
	status?: string;
}

export interface ApiResponse {
	webHosts: WebHost[];
}
