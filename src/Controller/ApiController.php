<?php

namespace App\Controller;

use App\Command\RefreshAllServicesCommand;
use App\Entity\WebHost\WebHost;
use App\Entity\WebHost\WebHostUrl;
use App\Message\CheckSSLCertificatesMessage;
use App\Message\FetchSwarmpitMessage;
use App\Message\HealthCheckMessage;
use App\Message\RedeployServicesMessage;
use App\Service\CsvExporter;
use App\Service\MercureUpdater;
use App\Service\RegistryAPI;
use App\Service\SwarmpitAPI;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryParameter;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Process\Process;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api')]
class ApiController extends AbstractController
{
    public const URL_REGEX = '([A-z0-9-\.])+';

    public function __construct(
        private readonly SwarmpitAPI $api,
        private readonly MercureUpdater $updater
    ) {
    }

    #[Route('/services')]
    public function index(
        #[Autowire(param: 'kernel.project_dir')] string $projectDir,
        #[MapQueryParameter('force')] bool $force = false
    ): JsonResponse {
        if ($force) {
            $process = new Process(['php', $projectDir . '/bin/console', RefreshAllServicesCommand::getDefaultName()]);
            $process->run();
        }

        $result = $this->api->getAllServices();

        return $this->json($result);
    }

    #[Route('/services/{serverName}/{serviceId}')]
    public function service(string $serverName, string $serviceId): JsonResponse
    {
        $code = 200;
        try {
            $this->api->setNoCache(true);
            $server = $this->api->getServer($serverName);
            $service = $this->api->getServiceAndTasksInfos($server, $serviceId, true);
            $service = $this->api->formatService($server, $service);

            $this->updater->updateService($service);
            $response = $service;
        } catch (\Exception $exception) {
            $this->updater->sendError($serviceId, $exception->getMessage());
            $response = [
                'id' => $serviceId,
                'exception' => $exception->getMessage(),
            ];
            $code = 503;
        }

        return $this->json($response, $code);
    }

    #[Route('/details/{serverName}/{service}')]
    public function details(string $serverName, string $service): JsonResponse
    {
        $details = $this->api->getDetails($serverName, $service);

        return $this->json($details);
    }

    #[Route('/redeploy/{serverName}/{serviceId}', methods: 'GET', requirements: ['serverName' => self::URL_REGEX, 'serviceId' => self::URL_REGEX])]
    public function redeploy(string $serverName, string $serviceId, Request $request, MessageBusInterface $bus): JsonResponse
    {
        $bus->dispatch(new RedeployServicesMessage([
            ['serverName' => $serverName, 'serviceId' => $serviceId],
        ], $request->query->get('tag')));

        return $this->json([
            'status' => 'OK',
        ]);
    }

    #[Route('/stacks/{serverName}/{stackId}/compose')]
    public function stackCompose(string $serverName, string $stackId): Response
    {
        $composeFileContent = $this->api->getStackCompose($serverName, $stackId);

        return new Response($composeFileContent);
    }

    #[Route('/redeploy', methods: 'POST')]
    public function redeployBatch(Request $request, MessageBusInterface $bus): JsonResponse
    {
        $bus->dispatch(new RedeployServicesMessage($request->toArray(), $request->query->get('tag')));

        return $this->json([
            'status' => 'OK',
        ]);
    }

    #[Route('/refresh', methods: 'POST')]
    public function refreshBatch(Request $request, MessageBusInterface $bus): JsonResponse
    {
        $bus->dispatch(new FetchSwarmpitMessage($request->toArray(), $request->query->get('tag')));

        return $this->json([
            'status' => 'OK',
        ]);
    }

    #[Route('/webHosts/refresh', methods: 'POST')]
    public function refreshWebHostsBatch(Request $request, MessageBusInterface $bus, ManagerRegistry $doctrine): JsonResponse
    {
        $webHostIds = array_map(fn ($item) => $item['webHostId'], $request->toArray());
        $entityManager = $doctrine->getManager();

        $webHostUrls = [];
        foreach ($webHostIds as $webHostId) {
            $webHost = $entityManager->getRepository(WebHost::class)->find($webHostId);
            if ($webHost) {
                foreach ($webHost->getUrls() as $url) {
                    $webHostUrls[] = ['webHostUrlId' => $url->getId()];
                }
            }
        }

        if (!empty($webHostUrls)) {
            $bus->dispatch(new HealthCheckMessage($webHostUrls));
            $bus->dispatch(new CheckSSLCertificatesMessage($webHostUrls));
        }

        return $this->json([
            'status' => 'OK',
        ]);
    }

    #[Route('/registry/tags/{image}', requirements: ['image' => '.+'])]
    public function registryTags(string $image, RegistryAPI $registryAPI): JsonResponse
    {
        $result = $registryAPI->getTags($image);

        return $this->json($result);
    }

    #[Route('/composer-usage', methods: ['POST'])]
    public function composerUsage(Request $request): JsonResponse
    {
        /** @var array{ package: string } $payload */
        $payload = $request->toArray();

        if (!isset($payload['package'])) {
            throw new BadRequestHttpException('Missing parameter : package');
        }

        $services = $this->api->getAllServices();
        $result = [];

        foreach ($services['services'] as $service) {
            if (isset($service['details']['composer']['installed']) && isset($service['details']['git']['url'])) {
                if (false !== array_search($payload['package'], array_column($service['details']['composer']['installed'], 'name'))) {
                    if (str_contains($service['details']['git']['url'], 'gitlab.alienor.net')) {
                        $result[] = str_replace('https://gitlab.alienor.net/', '', $service['details']['git']['url']);
                    }
                }
            }
        }

        $result = array_values(array_unique($result));

        sort($result);

        return $this->json($result);
    }

    #[Route('/download-csv', methods: ['POST'])]
    public function downloadCsv(Request $request, CsvExporter $csvExporter): Response
    {
        $payload = $request->toArray();

        $ids = [];
        if (isset($payload['ids'])) {
            $ids = $payload['ids'];
        }

        $csvContent = $csvExporter->generateCsv($ids);

        $response = new Response($csvContent);
        $response->headers->set('Content-Type', 'text/csv');
        $response->headers->set('Content-Disposition', 'attachment; filename="tdb-swarm.csv"');

        return $response;
    }

    #[Route('/webHosts/data')]
    public function webHostsData(ManagerRegistry $doctrine): JsonResponse
    {
        // TODO crééer une classe de Repository dédiée
        $webHosts = $doctrine->getManagerForClass(WebHost::class)->createQueryBuilder()
            ->select('w, u, c')
            ->from(WebHost::class, 'w')
            ->leftJoin('w.urls', 'u')
            ->leftJoin('w.configuration', 'c')
            ->orderBy('w.name', 'ASC')
            ->getQuery()
            ->getResult()
        ;

        return $this->json([
            'webHosts' => $webHosts,
        ]);
    }

    #[Route('/chrome-extension/list')]
    public function chromeExtensionList(ManagerRegistry $doctrine): JsonResponse
    {
        // TODO crééer une classe de Repository dédiée
        $webHosts = $doctrine->getManagerForClass(WebHostUrl::class)->createQueryBuilder()
            ->select('u')
            ->from(WebHostUrl::class, 'u')
            ->leftJoin('u.webHost', 'w')
            ->orderBy('u.url', 'ASC')
            ->getQuery()
            ->getResult()
        ;

        $result = array_map(function (WebHostUrl $webHostUrl) {
            return [
                'url' => $webHostUrl->getUrl(),
                'webHostId' => $webHostUrl->getWebHost()->getId(),
            ];
        }, $webHosts);

        return $this->json($result);
    }

    #[Route('/chrome-extension/data')]
    public function chromeExtensionData(ManagerRegistry $doctrine): JsonResponse
    {
        // TODO crééer une classe de Repository dédiée
        $webHosts = $doctrine->getManagerForClass(WebHost::class)->createQueryBuilder()
            ->select('w, u, c')
            ->from(WebHost::class, 'w')
            ->leftJoin('w.urls', 'u')
            ->leftJoin('w.configuration', 'c')
            ->orderBy('w.name', 'ASC')
            ->getQuery()
            ->getResult()
        ;

        return $this->json([
            'webHosts' => $webHosts,
        ], context: ['groups' => ['chrome-extension']]);
    }

    #[Route('/chrome-extension/data/{id}')]
    public function chromeExtensionDataForWebHost(WebHost $webHost): JsonResponse
    {
        return $this->json($webHost, context: ['groups' => ['chrome-extension']]);
    }
}
