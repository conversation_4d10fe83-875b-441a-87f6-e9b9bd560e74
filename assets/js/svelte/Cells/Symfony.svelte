<script lang="ts">
	interface Props {
		service: Service;
	}

	let { service }: Props = $props();

	let color = $derived.by(() => {
		let c = '';
		if (service.redeployedAllowed) {
			c = 'secondary';

			if (service?.details?.symfony?.latest_patch_version) {
				c = 'success';
				if (
					service?.details?.symfony?.latest_patch_version !== service?.details?.symfony?.version
				) {
					c = 'warning text-dark';
				}
				if (service?.details?.symfony.is_eoled) {
					c = 'danger';
				}
			}
		}

		return c;
	});
</script>

{#if service?.details?.symfony?.version}
	<div class="text-end">
		<span class="badge bg-{color}">{service.details.symfony.version}</span>
	</div>
{:else if service?.details}
	<div class="text-end"></div>
{:else}
	<div class="text-end">…</div>
{/if}
