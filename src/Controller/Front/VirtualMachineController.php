<?php

namespace App\Controller\Front;

use App\Entity\WebHost\VirtualMachine;
use App\Form\VirtualMachineType;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/virtual-machines', priority: 1)]
class VirtualMachineController extends AbstractController
{
    #[Route('/', name: 'app_virtual_machines_index')]
    public function index(ManagerRegistry $doctrine): Response
    {
        $virtualMachines = $doctrine->getRepository(VirtualMachine::class)->findAllWithConfigurationCount();

        return $this->render('virtualMachines/index.html.twig', [
            'virtualMachines' => $virtualMachines,
        ]);
    }

    #[Route('/create', name: 'app_virtual_machines_create')]
    public function create(Request $request, ManagerRegistry $doctrine): Response
    {
        $virtualMachine = new VirtualMachine();
        $form = $this->createForm(VirtualMachineType::class, $virtualMachine);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager = $doctrine->getManager();
            $entityManager->persist($virtualMachine);
            $entityManager->flush();

            $this->addFlash('success', 'Machine virtuelle créée avec succès.');
            return $this->redirectToRoute('app_virtual_machines_index');
        }

        return $this->render('virtualMachines/create.html.twig', [
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}/edit', name: 'app_virtual_machines_edit')]
    public function edit(VirtualMachine $virtualMachine, Request $request, ManagerRegistry $doctrine): Response
    {
        $form = $this->createForm(VirtualMachineType::class, $virtualMachine);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager = $doctrine->getManager();
            $entityManager->flush();

            $this->addFlash('success', 'Machine virtuelle modifiée avec succès.');
            return $this->redirectToRoute('app_virtual_machines_index');
        }

        return $this->render('virtualMachines/edit.html.twig', [
            'virtualMachine' => $virtualMachine,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}/delete', name: 'app_virtual_machines_delete', methods: ['POST'])]
    public function delete(VirtualMachine $virtualMachine, Request $request, ManagerRegistry $doctrine): Response
    {
        if ($this->isCsrfTokenValid('delete'.$virtualMachine->getId(), $request->request->get('_token'))) {
            $entityManager = $doctrine->getManager();
            
            // Vérifier s'il y a des configurations liées
            if ($virtualMachine->getConfigurations()->count() > 0) {
                $this->addFlash('error', 'Impossible de supprimer cette VM car elle est utilisée par des configurations.');
                return $this->redirectToRoute('app_virtual_machines_index');
            }
            
            $entityManager->remove($virtualMachine);
            $entityManager->flush();
            
            $this->addFlash('success', 'Machine virtuelle supprimée avec succès.');
        }

        return $this->redirectToRoute('app_virtual_machines_index');
    }

    #[Route('/{id}', name: 'app_virtual_machines_show')]
    public function show(VirtualMachine $virtualMachine): Response
    {
        return $this->render('virtualMachines/show.html.twig', [
            'virtualMachine' => $virtualMachine,
        ]);
    }
}
