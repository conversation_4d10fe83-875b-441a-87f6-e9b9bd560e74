<?php

namespace App\Service;

use App\Model\Server;
use App\Model\Swarmpit\SwarmpitService;
use App\Model\Swarmpit\SwarmpitTask;
use Psr\Cache\CacheItemPoolInterface;
use Spatie\Fork\Fork;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpKernel\Exception\ServiceUnavailableHttpException;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class SwarmpitAPI
{
    public const API_SERVICES = '/api/services';
    public const API_TASKS = '/api/tasks';
    public const API_STACKS = '/api/stacks';
    public const API_REDEPLOY = self::API_SERVICES . '/%s/redeploy';
    public const API_SERVICE_TASKS = self::API_SERVICES . '/%s/tasks';
    public const API_STACK_COMPOSE = self::API_STACKS . '/%s/compose';
    public const API_DETAILS = '%s/_anetversion/checker.php';
    public const API_DETAILS_PHPINFO = '%s/_anetversion/index.php';

    public const LINK_SERVICE = '#/services/%s';
    public const LINK_SERVICE_LOGS = self::LINK_SERVICE . '?log=1';

    public const EXCLUDED_STATUSES = ['shutdown', 'rejected', 'failed'];
    public const EXCLUDED_STACKS_URL = ['web-default'];

    private $client;
    private array $config;
    /** @var array|\App\Model\Server[] */
    private array $servers;
    private string $detailsAuth = '';
    private bool $noCache = false;

    public function __construct(
        HttpClientInterface $client,
        ParameterBagInterface $parameterBag,
        private readonly VersionCache $versionCache,
        private readonly SonarQubeApi $sonarQubeApi,
        CacheItemPoolInterface $swarmpitCache,
        #[Autowire(env: 'FETCH_CONCURRENCY')] private readonly int $concurrency
    ) {
        $this->client = new ApiCache($client, $swarmpitCache);
        $this->config = $parameterBag->get('swarmpit.config');
        $this->detailsAuth = $parameterBag->get('details_http_auth_password');
        $this->servers = array_map(function ($config) {
            return Server::create($config);
        }, $this->config['servers']);
    }

    public function getClient(Server $server)
    {
        return $this->client->withOptions([
            'base_uri' => $server->url,
            'verify_peer' => false,
            'verify_host' => false,
            'headers' => [
                'Authorization' => 'Bearer ' . $server->token,
            ],
            'timeout' => 3,
        ]);
    }

    public function setNoCache(bool $noCache = true)
    {
        $this->noCache = $noCache;
    }

    /**
     * Parcours tous les serveurs pour récupérer les informations de chacun des services.
     */
    public function getAllServices(): array
    {
        $services = [];
        $errors = [];
        foreach ($this->servers as $server) {
            try {
                $services = [...$services, ...$this->fetchServices($server)];
            } catch (\Exception $e) {
                $errors[] = [
                    'server' => $server->name,
                    'exception' => $e->getMessage(),
                ];
            }
        }

        return [
            'errors' => $errors,
            'services' => $services,
        ];
    }

    /**
     * Parcours tous les serveurs pour récupérer les informations de chacun des services en parallèle.
     */
    public function getAllServicesAsync($warmupOnly = false): array
    {
        $promises = [];
        foreach ($this->servers as $server) {
            dump($server->name);
            $promises[] = function () use ($server) {
                try {
                    $this->fetchServices($server);
                } catch (\Exception $e) {
                    return [
                        'server' => $server->name,
                        'exception' => $e->getMessage(),
                    ];
                }

                return null;
            };
        }

        Fork::new()
            ->concurrent($this->concurrency)
            ->run(...$promises)
        ;

        if ($warmupOnly) {
            return [];
        }

        // On récupère tout depuis le cache maintenant que c'est préchargé
        $cache = $this->noCache;
        $this->setNoCache(false);
        $response = $this->getAllServices();
        $this->setNoCache($cache);

        return $response;
    }

    /**
     * Récupère la liste des services d'un serveur Docker Swarm.
     */
    public function fetchServices($server): array
    {
        $client = $this->getClient($server);

        $response = $client->request('GET', self::API_SERVICES, force: $this->noCache);

        $services = $response->toArray();

        return $this->formatServices($server, $services, $this->getTasks($server));
    }

    /**
     * Récupère la liste des tasks d'un serveur Docker Swarm.
     */
    public function getTasks($server): array
    {
        $response = $this->getClient($server)->request('GET', self::API_TASKS, force: $this->noCache);

        $tasks = array_map(function ($taskData) {
            return SwarmpitTask::fromJson($taskData);
        }, $response->toArray());

        return array_values(array_filter($tasks, function (SwarmpitTask $task) {
            return !in_array($task->state, self::EXCLUDED_STATUSES, true);
        }));
    }

    /**
     * Transforme la liste des services pour générer un json bien formatté.
     */
    public function formatServices(Server $server, array $services, array $tasks): array
    {
        $services = array_map(function ($serviceData) use ($server, $tasks) {
            $service = SwarmpitService::fromJson($serviceData);

            $service->tasks = array_values(array_filter($tasks, function (SwarmpitTask $task) use ($service) {
                return $task->serviceName === $service->serviceName;
            }));

            try {
                if ($this->client->hasCachedItem($this->getDetailsCacheKey($server->name, $service))) {
                    $service->details = $this->fetchDetails($server->name, $service);
                }
            } catch (\Exception) {
            }

            return $this->formatService(
                $server,
                $service
            );
        }, $services);

        return $services;
    }

    public function formatService(
        Server $server,
        SwarmpitService $service
    ): array {
        $serviceShortName = str_replace($service->stack . '_', '', $service->serviceName);
        $urls = $this->extractUrls($service);
        $phpinfo = null;

        if (in_array($service->stack, self::EXCLUDED_STACKS_URL, true)) {
            $urls = [$this->getWebDefaultDetailsUrl($this->getServer($server->name))];
        }
        if (count($urls)) {
            $phpinfo = sprintf(self::API_DETAILS_PHPINFO, $urls[0]);
        }

        return [
            'id' => $service->id,
            'env' => $server->env,
            'server' => $server->name,
            'name' => $serviceShortName,
            'stack' => $service->stack,
            'urls' => $urls,
            'middleware' => $this->extractMiddleware($service),
            'status' => $service->status,
            'repository' => $service->repository,
            'replicas' => $service->replicas,
            'resources' => $service->resources,
            'placement' => $this->extractPlacement($service),
            'updatedAt' => $service->updatedAt,
            'variables' => $service->variables,
            'links' => [
                'service' => $server->url . sprintf(self::LINK_SERVICE, $service->serviceName),
                'logs' => $server->url . sprintf(self::LINK_SERVICE_LOGS, $service->serviceName),
                'phpinfo' => $phpinfo,
            ],
            'redeployedAllowed' => $service->isAllowedToRedeploy(),
            'tasks' => array_map(function (SwarmpitTask $task) use ($server, $service) {
                return $this->formatTask($server, $service, $task);
            }, $service->tasks),
            'details' => $service->details ?? null,
        ];
    }

    public function formatTask(
        Server $server,
        SwarmpitService $service,
        SwarmpitTask $task,
    ): array {
        // Génération de la commande SSH selon le type de service
        $command = 'bash';
        if (str_contains($service->repository->image, 'mariadb')) {
            $db = null;
            $username = null;
            $password = null;
            foreach ($service->variables as $variable) {
                if ('MYSQL_DATABASE' === $variable['name']) {
                    $db = $variable['value'];
                }
                if ('MYSQL_PASSWORD' === $variable['name']) {
                    $password = $variable['value'];
                }
                if ('MYSQL_USER' === $variable['name']) {
                    $username = $variable['value'];
                }
            }
            $command = sprintf(
                'mariadb -u %s -p%s %s',
                $username,
                $password,
                $db,
            );
        }
        if (str_starts_with($service->repository->image, 'node')) {
            $command = 'sh';
        }
        $sshCommand = sprintf(
            'ssh -tt devadmin@%s "ssh -tt devadmin@\$(docker inspect -f {{.Status.Addr}} %s) docker exec -it %s.%s %s"',
            parse_url($server->url)['host'],
            $task->nodeId,
            $task->taskName,
            $task->id,
            $command
        );

        return [
            'id' => $task->id,
            'nodeName' => $task->nodeName,
            'state' => $task->state,
            'stats' => $task->stats,
            'sshCommand' => $sshCommand,
            'dockerToolsCommand' => DockerTools::getDockerToolsCommand($server, $service),
        ];
    }

    /**
     * Récupère les infos d'un service d'après son serveur et son id.
     */
    public function getService(Server $server, string $serviceId): SwarmpitService
    {
        $client = $this->getClient($server);
        $response = $client->request('GET', sprintf('%s/%s', self::API_SERVICES, $serviceId), force: $this->noCache);

        return SwarmpitService::fromJson($response->toArray());
    }

    /**
     * Récupère les infos d'un service d'après son serveur et son id.
     */
    public function getServiceAndTasksInfos(Server $server, string $serviceId, bool $withDetails = false): SwarmpitService
    {
        $service = $this->getService($server, $serviceId);
        $service->tasks = $this->getServiceTasks($server, $service);

        if ($withDetails || $this->client->hasCachedItem($this->getDetailsCacheKey($server->name, $service))) {
            $service->details = $this->fetchDetails($server->name, $service, $withDetails);
        }

        return $service;
    }

    /**
     * Récupère les tasks d'un service.
     */
    public function getServiceTasks(Server $server, SwarmpitService $service): array
    {
        $client = $this->getClient($server);

        $response = $client->request('GET', sprintf(self::API_SERVICE_TASKS, $service->id), force: $this->noCache);

        $tasks = array_values(array_filter($response->toArray(), function (array $taskData) {
            return !in_array($taskData['state'], self::EXCLUDED_STATUSES, true);
        }));

        return array_map(function ($taskData) {
            return SwarmpitTask::fromJson($taskData);
        }, $tasks);
    }

    /**
     * Recupère le serveur depuis la config d'après son nom.
     */
    public function getServer(string $serverName): Server
    {
        $serverKey = array_search($serverName, array_column($this->servers, 'name'));
        if (false !== $serverKey) {
            return $this->servers[$serverKey];
        }
        throw new \Exception(sprintf('Invalid server name : %s', $serverName));
    }

    /**
     * Permet de redeployer un service docker-swarm.
     */
    public function redeploy(string $serverName, string $serviceId, ?string $tag = null)
    {
        $client = $this->getClient($this->getServer($serverName));

        $response = $client->request('POST', sprintf(self::API_REDEPLOY, $serviceId), [
            'query' => ['tag' => $tag],
        ], force: true);

        return 200 === $response->getStatusCode();
    }

    public function getStackCompose(string $serverName, string $stackId): string
    {
        $client = $this->getClient($this->getServer($serverName));
        $response = $client->request('GET', sprintf(self::API_STACK_COMPOSE, $stackId), force: $this->noCache);

        return $response->toArray()['spec']['compose'];
    }

    /*** Extraction methods ***/

    /**
     * Extrait les urls dans les labels si ils contiennent Host('*').
     */
    public function extractUrls(SwarmpitService $service): array
    {
        $urls = [];
        foreach ($service->labels as $label) {
            if (!in_array($service->stack, self::EXCLUDED_STACKS_URL, true) && str_starts_with((string) $label->value, 'Host(')) {
                if (
                    1 == preg_match("/Host\(`(.*?)`\)/", (string) $label->value, $match)
                ) {
                    $values = array_map(function ($url) {
                        $url = trim($url, "\"'` \t\n");
                        if (!str_starts_with($url, 'http')) {
                            $url = sprintf('http://%s', $url);
                        }

                        return $url;
                    }, explode(',', $match[1]));
                    $urls = [...$urls, ...$values];
                }
            }
        }

        return array_unique($urls);
    }

    /**
     * Extrait le type de serveur cible depuis les règles de placement.
     */
    public function extractPlacement(SwarmpitService $service): ?string
    {
        if (isset($service->deployment->placement[0])) {
            return str_contains((string) $service->deployment->placement[0]->rule, 'manager') ? 'Manager' : 'Worker';
        }

        return null;
    }

    /**
     * Extraction du middle depuis les labels.
     */
    public function extractMiddleware(SwarmpitService $service): ?string
    {
        foreach ($service->labels as $label) {
            if (str_contains((string) $label->name, 'https.middleware')) {
                return $label->value;
            }
        }

        return null;
    }

    /**
     * Récupère les informations générées par swarm-checker sur le service en question.
     */
    public function getDetails(string $serverName, string $serviceId): array
    {
        $service = $this->getService($this->getServer($serverName), $serviceId);

        if ($service) {
            return $this->fetchDetails($serverName, $service);
        }

        throw new ServiceUnavailableHttpException();
    }

    public function isPhp56(SwarmpitService $service)
    {
        return str_starts_with($service->repository->tag, '5.6') && str_contains($service->repository->name, 'web_php');
    }

    public function isPhp70(SwarmpitService $service)
    {
        return str_starts_with($service->repository->tag, '7.0') && str_contains($service->repository->name, 'web_php');
    }

    /**
     * Vérification que le https est activé en fonction des labels traefik.
     */
    public function isHttps(SwarmpitService $service)
    {
        return in_array('https', array_column($service->labels, 'value'));
    }

    public function fetchDetails(string $serverName, SwarmpitService $service, $noCache = false): array
    {
        if (!$service->hasRemoteDetails()) {
            return [];
        }

        $cacheKey = $this->getDetailsCacheKey($serverName, $service);

        $urls = $this->extractUrls($service);
        if (in_array($service->stack, self::EXCLUDED_STACKS_URL, true)) {
            $urls = [$this->getWebDefaultDetailsUrl($this->getServer($serverName))];
        }
        if (count($urls)) {
            try {
                $firstUrl = $urls[0];
                // On appelle l'url des détails en https si le https est activé pour ce service
                if ($this->isHttps($service)) {
                    $firstUrl = str_replace('http://', 'https://', $firstUrl);
                }
                // Surchage pour php 5.6 car on ne peut pas récupérer les détails dessus
                if ($this->isPhp56($service)) {
                    return $this->enhanceDetails($this->client->setCacheResponse($cacheKey, ['php' => ['version' => '5.6.40']]));
                }
                // Surchage pour php 7.0 car on ne peut pas récupérer les détails dessus
                if ($this->isPhp70($service)) {
                    return $this->enhanceDetails($this->client->setCacheResponse($cacheKey, ['php' => ['version' => '7.0.33']]));
                }
                $response = $this->client->request('GET', sprintf(self::API_DETAILS, $firstUrl), [
                    'headers' => [
                        'Authorization' => sprintf('Basic %s', $this->detailsAuth),
                    ],
                    'verify_peer' => false,
                    'verify_host' => false,
                ], $cacheKey, force: $noCache, expiresAfter: 86400 * 365);

                return $this->enhanceDetails($response->toArray());
            } catch (\Exception $e) {
                if ('Response body is empty.' === $e->getMessage()) {
                    return [];
                }
                throw $e;
            }
        } else {
            return [];
        }
        throw new ServiceUnavailableHttpException();
    }

    public function enhanceDetails(array $details)
    {
        $phpVersion = $details['php']['version'] ?? null;
        if ($phpVersion) {
            $major = substr((string) $phpVersion, 0, strrpos((string) $phpVersion, '.'));
            try {
                $infos = $this->versionCache->getPhpVersionInfos($major);
                if ($infos && !isset($infos['error'])) {
                    $details['php']['is_eoled'] = !in_array($major, $infos['supported_versions']);
                    $details['php']['latest_patch_version'] = $infos['version'];
                    $details['php']['is_latest'] = $infos['version'] === $phpVersion;
                }
            } catch (\Exception) {
            }
        }

        $symfonyVersion = $details['symfony']['version'] ?? null;
        if ($symfonyVersion) {
            try {
                $infos = $this->versionCache->getSymfonyVersionInfos($symfonyVersion);
                $details['symfony']['latest_patch_version'] = $infos['latest_patch_version'];
                $details['symfony']['is_latest'] = $infos['is_latest'];
                $details['symfony']['is_lts'] = $infos['is_lts'];
                $details['symfony']['is_eomed'] = $infos['is_eomed'];
                $details['symfony']['is_eoled'] = $infos['is_eoled'];
                $details['symfony']['eom'] = $infos['eom'];
                $details['symfony']['eol'] = $infos['eol'];
            } catch (\Exception) {
            }
        }

        $projectKey = $details['sonarQube']['projectKey'] ?? null;
        if ($projectKey) {
            try {
                $details['sonarQube']['url'] = $this->sonarQubeApi->getProjectUrl($projectKey);
                $details['sonarQube']['badges'] = $this->sonarQubeApi->getBadges($projectKey);
            } catch (\Exception) {
            }
        }

        $securityChecker = $details['local-php-security-checker']['advisories'] ?? null;
        if ($securityChecker) {
            // On refait array_values car le résultat peut contenir un tableau non trié qui se transforme en objet côté JS
            $details['local-php-security-checker']['advisories'] = array_map(function ($advisories) {
                return array_values($advisories);
            }, $securityChecker);
        }

        return $details;
    }

    public function getDetailsCacheKey(string $serverName, SwarmpitService $service): string
    {
        return sprintf('details_%s_%s', $serverName, $service->serviceName);
    }

    /** Pour les web-default on récupère les détails depuis le container par défaut */
    public function getWebDefaultDetailsUrl(Server $server)
    {
        return str_replace('swarmpit', 'web-default', $server->url);
    }
}
