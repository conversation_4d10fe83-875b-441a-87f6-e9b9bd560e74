<script lang="ts">
	interface Props {
		service: Service;
		onclick?: (stack: string) => void;
		onStackClick?: (service: Service) => void;
		services: () => Service[];
	}

	let {
		service,
		onclick = () => {},
		onStackClick = () => {},
		services = () => []
	}: Props = $props();

	const servicesInStack = $derived(
		services()?.filter((s) => s.stack === service.stack && s.server === service.server).length
	);
</script>

<div>
	<span
		class="badge bg-secondary"
		onclick={() => onclick(service.stack)}
		style="cursor: pointer"
		title="Voir les services de la stack"
	>
		<i class="fa fa-magnifying-glass"></i>
		{servicesInStack}
	</span>
	<span
		class="text-primary"
		style="cursor: pointer;"
		onclick={() => onStackClick(service)}
		title="Voir le fichier compose de la stack"
	>
		{service.stack}
	</span>
</div>
