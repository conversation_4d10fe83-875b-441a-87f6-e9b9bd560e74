<?php

namespace App\Form;

use App\Entity\WebHost\VirtualMachine;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints as Assert;

class VirtualMachineType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name', TextType::class, [
                'label' => 'Nom',
                'required' => false,
                'attr' => [
                    'placeholder' => 'ex: Serveur de production',
                ],
            ])
            ->add('ip', TextType::class, [
                'label' => 'Adresse IP',
                'required' => true,
                'constraints' => [
                    new Assert\NotBlank(['message' => 'L\'adresse IP est obligatoire.']),
                    new Assert\Ip(['message' => 'Veuillez saisir une adresse IP valide.']),
                ],
                'attr' => [
                    'placeholder' => 'ex: *************',
                ],
            ])
            ->add('phpVersion', TextType::class, [
                'label' => 'Version PHP',
                'required' => false,
                'attr' => [
                    'placeholder' => 'ex: 8.2, 8.1, 7.4',
                ],
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => VirtualMachine::class,
        ]);
    }
}
