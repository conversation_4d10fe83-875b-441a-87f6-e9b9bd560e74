#!/bin/bash

# Script pour construire et packager l'extension Chrome en CRX
set -e

# Variables
EXTENSION_DIR="chrome-extension/dist"
BUILD_DIR="build/chrome-extension"
DIST_DIR="dist"
EXTENSION_NAME="alienor-webhost-extension"
VERSION=$(grep '"version"' $EXTENSION_DIR/manifest.json | sed 's/.*"version": "\([^"]*\)".*/\1/')

echo "Building Chrome Extension v$VERSION..."

# Créer les répertoires de build
mkdir -p $BUILD_DIR
mkdir -p $DIST_DIR

# Copier les fichiers de l'extension
cp -r $EXTENSION_DIR/* $BUILD_DIR/

# Vérifier si crx3 est installé
if ! command -v crx3 &> /dev/null; then
    echo "crx3 not found. Please install it with: npm install -g crx3"
    exit 0
fi

# Générer une clé privée si elle n'existe pas
if [ ! -f "scripts/chrome-extension.pem" ]; then
    echo "Generating private key..."
    openssl genrsa -out scripts/chrome-extension.pem 2048
fi

# Packager en CRX avec crx3
echo "Packaging as CRX with crx3..."
crx3 --crx "$DIST_DIR/${EXTENSION_NAME}-${VERSION}.crx" --key scripts/chrome-extension.pem $BUILD_DIR

echo "Extension packaged successfully:"
echo "- CRX: $DIST_DIR/${EXTENSION_NAME}-${VERSION}.crx"
