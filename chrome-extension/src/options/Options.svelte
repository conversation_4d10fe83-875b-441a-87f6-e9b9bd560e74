<script lang="ts">
	import { onMount } from 'svelte';
	import {
		getSettings,
		saveSettings,
		resetSettings,
		type ExtensionSettings
	} from '../services/settings.js';

	// État des paramètres
	let settings = $state<ExtensionSettings>({ dockerToolsEnabled: true });
	let loading = $state(true);
	let saving = $state(false);
	let message = $state<string | null>(null);
	let messageType = $state<'success' | 'error'>('success');

	// Charger les paramètres au montage
	onMount(async () => {
		try {
			settings = await getSettings();
		} catch (error) {
			console.error('Erreur lors du chargement des paramètres:', error);
			showMessage('Erreur lors du chargement des paramètres', 'error');
		} finally {
			loading = false;
		}
	});

	// Sauvegarder les paramètres
	async function handleSave() {
		if (saving) return;

		try {
			saving = true;
			await saveSettings(settings);
			showMessage('Paramètres sauvegardés avec succès !', 'success');
		} catch (error) {
			console.error('Erreur lors de la sauvegarde:', error);
			showMessage('Erreur lors de la sauvegarde des paramètres', 'error');
		} finally {
			saving = false;
		}
	}

	// Réinitialiser les paramètres
	async function handleReset() {
		if (saving) return;

		if (!confirm('Êtes-vous sûr de vouloir réinitialiser tous les paramètres ?')) {
			return;
		}

		try {
			saving = true;
			await resetSettings();
			settings = await getSettings();
			showMessage('Paramètres réinitialisés avec succès !', 'success');
		} catch (error) {
			console.error('Erreur lors de la réinitialisation:', error);
			showMessage('Erreur lors de la réinitialisation des paramètres', 'error');
		} finally {
			saving = false;
		}
	}

	// Afficher un message temporaire
	function showMessage(text: string, type: 'success' | 'error') {
		message = text;
		messageType = type;
		setTimeout(() => {
			message = null;
		}, 3000);
	}
</script>

<div class="container">
	<header class="header">
		<h1>Configuration</h1>
		<p>Alienor WebHost Extension</p>
	</header>

	{#if loading}
		<div class="loading">
			<div class="spinner"></div>
			<p>Chargement des paramètres...</p>
		</div>
	{:else}
		<main class="content">
			<section class="settings-section">
				<h2>Fonctionnalités</h2>

				<div class="setting-item">
					<div class="setting-info">
						<label for="docker-tools" class="setting-label"> Activer Docker Tools </label>
						<p class="setting-description">
							Affiche les liens d'ouverture avec Docker Tools dans la popup (nécessite que le
							protocole d'ouverture des liens "dockertools://" soit installé)
						</p>
					</div>
					<div class="setting-control">
						<label class="toggle">
							<input id="docker-tools" type="checkbox" bind:checked={settings.dockerToolsEnabled} />
							<span class="toggle-slider"></span>
						</label>
					</div>
				</div>
			</section>

			<div class="actions">
				<button class="btn btn-primary" onclick={handleSave} disabled={saving}>
					{saving ? 'Sauvegarde...' : 'Sauvegarder'}
				</button>

				<button class="btn btn-secondary" onclick={handleReset} disabled={saving}>
					Réinitialiser
				</button>
			</div>

			{#if message}
				<div class="message message-{messageType}">
					{message}
				</div>
			{/if}
		</main>
	{/if}
</div>

<style>
	:global(body) {
		margin: 0;
		padding: 0;
		font-family:
			-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
		background-color: #f5f5f5;
		color: #333;
		line-height: 1.6;
	}

	.container {
		max-width: 600px;
		margin: 0 auto;
		background: white;
		min-height: 100vh;
	}

	.header {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
		padding: 2rem;
		text-align: center;
	}

	.header h1 {
		margin: 0 0 0.5rem 0;
		font-size: 2rem;
		font-weight: 600;
	}

	.header p {
		margin: 0;
		opacity: 0.9;
		font-size: 1rem;
	}

	.loading {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 3rem;
		text-align: center;
	}

	.spinner {
		width: 32px;
		height: 32px;
		border: 3px solid #f3f3f3;
		border-top: 3px solid #667eea;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin-bottom: 1rem;
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}

	.content {
		padding: 2rem;
	}

	.settings-section {
		margin-bottom: 2rem;
	}

	.settings-section h2 {
		margin: 0 0 1.5rem 0;
		font-size: 1.25rem;
		font-weight: 600;
		color: #333;
		border-bottom: 2px solid #667eea;
		padding-bottom: 0.5rem;
	}

	.setting-item {
		display: flex;
		align-items: flex-start;
		justify-content: space-between;
		padding: 1.5rem;
		border: 1px solid #e1e5e9;
		border-radius: 8px;
		background: #fafbfc;
		margin-bottom: 1rem;
	}

	.setting-info {
		flex: 1;
		margin-right: 1rem;
	}

	.setting-label {
		display: block;
		font-weight: 600;
		font-size: 1rem;
		color: #333;
		margin-bottom: 0.25rem;
		cursor: pointer;
	}

	.setting-description {
		margin: 0;
		font-size: 0.875rem;
		color: #666;
	}

	.setting-control {
		flex-shrink: 0;
	}

	.toggle {
		position: relative;
		display: inline-block;
		width: 50px;
		height: 24px;
		cursor: pointer;
	}

	.toggle input {
		opacity: 0;
		width: 0;
		height: 0;
	}

	.toggle-slider {
		position: absolute;
		cursor: pointer;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #ccc;
		transition: 0.3s;
		border-radius: 24px;
	}

	.toggle-slider:before {
		position: absolute;
		content: '';
		height: 18px;
		width: 18px;
		left: 3px;
		bottom: 3px;
		background-color: white;
		transition: 0.3s;
		border-radius: 50%;
	}

	.toggle input:checked + .toggle-slider {
		background-color: #667eea;
	}

	.toggle input:checked + .toggle-slider:before {
		transform: translateX(26px);
	}

	.actions {
		display: flex;
		gap: 1rem;
		margin-top: 2rem;
	}

	.btn {
		padding: 0.75rem 1.5rem;
		border: none;
		border-radius: 6px;
		font-size: 0.875rem;
		font-weight: 600;
		cursor: pointer;
		transition: all 0.2s;
		text-decoration: none;
		display: inline-flex;
		align-items: center;
		justify-content: center;
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.btn-primary {
		background: #667eea;
		color: white;
	}

	.btn-primary:hover:not(:disabled) {
		background: #5a6fd8;
		transform: translateY(-1px);
	}

	.btn-secondary {
		background: #6c757d;
		color: white;
	}

	.btn-secondary:hover:not(:disabled) {
		background: #5a6268;
		transform: translateY(-1px);
	}

	.message {
		margin-top: 1rem;
		padding: 0.75rem 1rem;
		border-radius: 6px;
		font-size: 0.875rem;
		font-weight: 500;
	}

	.message-success {
		background: #d4edda;
		color: #155724;
		border: 1px solid #c3e6cb;
	}

	.message-error {
		background: #f8d7da;
		color: #721c24;
		border: 1px solid #f5c6cb;
	}
</style>
