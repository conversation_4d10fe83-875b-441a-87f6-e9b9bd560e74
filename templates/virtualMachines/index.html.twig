{% extends 'front.html.twig' %}

{% block title %}Machines virtuelles{% endblock %}

{% block body %}
    <div class="container-sm pt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>Machines virtuelles</h2>
            <a href="{{ path('app_virtual_machines_create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Ajouter une VM
            </a>
        </div>

        {% for message in app.flashes('success') %}
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}

        {% for message in app.flashes('error') %}
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}

        <div class="card">
            <div class="card-body">
                {% if virtualMachines|length > 0 %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Nom</th>
                                    <th>Adresse IP</th>
                                    <th>Version PHP</th>
                                    <th>Configurations liées</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for vm in virtualMachines %}
                                    <tr>
                                        <td>
                                            {% if vm.name %}
                                                {{ vm.name }}
                                            {% else %}
                                                <em class="text-muted">Sans nom</em>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <code>{{ vm.ip }}</code>
                                        </td>
                                        <td>
                                            {% if vm.phpVersion %}
                                                <span class="badge bg-info">PHP {{ vm.phpVersion }}</span>
                                            {% else %}
                                                <span class="text-muted">Non définie</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ vm.configurations|length }}</span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="{{ path('app_virtual_machines_show', {id: vm.id}) }}" 
                                                   class="btn btn-outline-info" title="Voir">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ path('app_virtual_machines_edit', {id: vm.id}) }}" 
                                                   class="btn btn-outline-primary" title="Modifier">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                {% if vm.configurations|length == 0 %}
                                                    <form method="post" action="{{ path('app_virtual_machines_delete', {id: vm.id}) }}" 
                                                          style="display: inline;" 
                                                          onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cette VM ?')">
                                                        <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ vm.id) }}">
                                                        <button type="submit" class="btn btn-outline-danger" title="Supprimer">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                {% else %}
                                                    <button type="button" class="btn btn-outline-danger" disabled title="Impossible de supprimer (configurations liées)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-server fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucune machine virtuelle</h5>
                        <p class="text-muted">Commencez par ajouter votre première VM.</p>
                        <a href="{{ path('app_virtual_machines_create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Ajouter une VM
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
{% endblock %}
